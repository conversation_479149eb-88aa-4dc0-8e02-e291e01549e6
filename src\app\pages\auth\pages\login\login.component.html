<section
  class="flex flex-col justify-center items-center w-full h-full bg-[url('/assets/bg-login.webp')] bg-no-repeat bg-center bg-cover relative"
>
  <div
    class="w-full max-w-[500px] p-10 bg-white text-center"
  >
    <div class="mb-2.5">
      <img
        src="assets/logovector-MIDAS.svg"
        alt="Midas Solutions Center"
        class="w-[220px] block mx-auto"
      />
    </div>

    <h2 class="text-xl font-bold text-black mb-8">INTRANET CRM MIDAS</h2>

    <form #f="ngForm" (ngSubmit)="loginUsuario(f)">
      <!-- Campo Email/Usuario -->
      <div class="mb-5 flex items-center relative bg-gray-100 rounded-md p-2.5">
        <mat-icon class="mr-2.5 text-[#002f6c]">email</mat-icon>
        <input
          type="text"
          placeholder="Ingrese su correo"
          name="username"
          ngModel
          required
          class="w-full p-2.5 border-none bg-transparent text-base outline-none"
        />
      </div>

      <!-- Campo Contraseña con icono de visibilidad -->
      <div class="mb-5 flex items-center relative bg-gray-100 rounded-md p-2.5">
        <mat-icon class="mr-2.5 text-[#002f6c]">lock</mat-icon>
        <input
          [type]="hidePassword ? 'password' : 'text'"
          placeholder="Ingrese su contraseña"
          name="password"
          ngModel
          required
          class="w-full p-2.5 border-none bg-transparent text-base outline-none"
        />
        <mat-icon
          class="absolute right-2.5 cursor-pointer text-[#002f6c] hover:text-[#0148a4]"
          (click)="hidePassword = !hidePassword"
        >
          {{ hidePassword ? "visibility_off" : "visibility" }}
        </mat-icon>
      </div>

      <button
        type="submit"
        class="w-full py-3 bg-[#002f6c] text-white font-bold border-none rounded-md cursor-pointer text-base mt-2.5 hover:bg-[#0148a4]"
      >
        Ingresar
      </button>
    </form>
  </div>
  <div class="mt-5 text-center">
    <p class="m-0 text-xs text-white text-shadow">
      © {{ currentYear }} Midas Solutions Center. Creado por Desarrolladores
      Midas
    </p>
  </div>
</section>
