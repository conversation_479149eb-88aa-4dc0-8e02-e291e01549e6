/* You can add global styles to this file, and also import other style files */
@import "@angular/material/prebuilt-themes/indigo-pink.css";

/* Tailwind CSS */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Estilos personalizados para Material Angular en modo oscuro ahora están integrados directamente */
html,
body {
  height: 100%;
  margin: 0 auto;
  padding: 0;
  background-color: #eef1f9;
}

/* Aplicar fuente Poppins a todos los elementos del menú lateral */
.mat-nav-list .mat-list-item span {
  font-family: "Poppins", sans-serif !important;
  font-size: 14px !important;
  font-weight: 500 !important;
}

/* Estilos para el espaciado en el menú de Tipificaciones */
.tipificaciones-panel .mat-expansion-panel-body .mat-list-item-content {
  height: auto !important;
}

/* Estilo específico para los elementos de Tipificaciones */
.tipificaciones-item {
  display: block !important;
  height: auto !important;
}

/* Ajustar el espaciado vertical en el cuerpo del panel */
.tipificaciones-panel .mat-expansion-panel-body {
  padding: 0.5rem 0 !important;
}

/* Estilos para el texto e iconos en el menú de Tipificaciones */
.tipificaciones-panel .mat-expansion-panel-body .navegacion-list-label {
  font-size: 0.75rem !important;
  line-height: 1.2 !important;
}

/* Estilos globales para tablas modernas */
.modern-table-container {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  margin-bottom: 1.5rem;
  transition: box-shadow 0.3s ease;
}

.modern-table-container:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
}

.modern-table {
  width: 100%;
}

.modern-table .mat-header-cell {
  background-color: #f8f9fa;
  color: #495057;
  font-weight: 600;
  font-size: 0.875rem;
  padding: 1rem 1rem;
  border-bottom: 2px solid #e9ecef;
}

.modern-table .mat-cell {
  padding: 0.875rem 1rem;
  font-size: 0.875rem;
  color: #212529;
  border-bottom: 1px solid #f1f3f5;
}

.modern-table .mat-row:hover {
  background-color: #f8f9fa;
}

/* Estilos para botones de acción */
.action-buttons {
  display: flex;
  gap: 0.5rem;
  justify-content: flex-start;
  align-items: center;
}

/* Estilos para chips de conteo */
.count-chip {
  min-width: 32px;
  justify-content: center;
}

/* Estilos para modales modernos */
.modern-modal {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  padding: 0;
  max-width: 90%;
  width: 600px;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* Estilos específicos para el modal de edición de anuncios */
.anuncio-edit-dialog {
  width: 800px !important;
  max-width: 95% !important;
}

.modern-modal .mat-dialog-container {
  padding: 0;
  border-radius: 0.5rem;
  overflow: hidden;
  background-color: white;
  color: #333;
}

.modern-modal .mat-dialog-title {
  margin: 0;
  padding: 16px 24px;
  background-color: #f5f5f5;
  color: #333;
  font-size: 20px;
  font-weight: 500;
  border-bottom: 1px solid #e0e0e0;
}

.modern-modal .mat-dialog-content {
  padding: 24px;
  margin: 0;
  max-height: 65vh;
}

/* Los estilos para el diálogo de registro de usuarios ahora están en user-registration-dialog.scss */

/* Estilos específicos para el diálogo de anuncios en modo oscuro */
.anuncio-edit-dialog.dark-theme .mat-dialog-container {
  background-color: var(--sidenav-dark) !important;
}

.modern-modal .mat-dialog-actions {
  padding: 16px 24px;
  margin: 0;
  border-top: 1px solid #e0e0e0;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.modern-modal .mat-form-field-appearance-outline .mat-form-field-outline {
  color: rgba(0, 0, 0, 0.12);
}

.modern-modal
  .mat-form-field-appearance-outline.mat-focused
  .mat-form-field-outline-thick {
  color: #3f51b5;
}

.modern-modal .mat-form-field-appearance-outline .mat-form-field-infix {
  padding: 0.75em 0 0.75em 0;
}

.modern-modal .mat-form-field-label-wrapper {
  top: -1.5em;
}

.modern-modal
  .mat-form-field-appearance-outline.mat-form-field-can-float.mat-form-field-should-float
  .mat-form-field-label {
  transform: translateY(-1.1em) scale(0.75);
}

/* Dark Theme Variables */
:root {
  --background-light: #eef1f9;
  --background-dark: #0e1c33;
  --text-light: #000000;
  --text-dark: #ffffff;
  --primary-light: #0277bd;
  --primary-dark: #1e4976;
  --sidenav-light: #f0f5ff;
  --sidenav-dark: #0a1628;
}

/* Eliminamos las transiciones para evitar saltos */

/* Estilos globales para scrollbars */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #555;
}

/* Dark Theme Class */
.dark-theme {
  background-color: var(--background-dark) !important;
  color: var(--text-dark) !important;

  /* Scrollbars en modo oscuro */
  ::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.2) !important;
  }

  ::-webkit-scrollbar-thumb {
    background: rgba(77, 171, 245, 0.5) !important; /* Color azulino */
  }

  ::-webkit-scrollbar-thumb:hover {
    background: rgba(
      77,
      171,
      245,
      0.7
    ) !important; /* Color azulino más intenso al hover */
  }

  .app-sidenav-container {
    background-color: var(--background-dark) !important;
  }

  /* Estilos globales para campos de formulario */
  input,
  textarea,
  select,
  .mat-select-value,
  .mat-input-element {
    color: #ffffff !important;
    caret-color: #ffffff !important;
  }

  .mat-form-field-flex {
    background-color: transparent !important;
  }

  .mat-form-field-label {
    color: rgba(255, 255, 255, 0.7) !important;
  }

  .mat-form-field-appearance-outline .mat-form-field-outline {
    color: rgba(255, 255, 255, 0.3) !important;
  }

  .mat-form-field-appearance-outline .mat-form-field-outline-thick {
    color: #1e4976 !important;
  }

  .mat-form-field-appearance-outline.mat-focused .mat-form-field-outline-thick {
    color: #1e4976 !important;
  }

  .mat-form-field-appearance-outline.mat-form-field-invalid.mat-form-field-invalid
    .mat-form-field-outline-thick {
    color: #f44336 !important;
  }

  .mat-form-field-appearance-outline .mat-form-field-infix {
    padding: 0.5em 0 0.5em 0 !important;
  }

  .mat-form-field-subscript-wrapper {
    color: rgba(255, 255, 255, 0.7) !important;
  }

  .mat-form-field-appearance-outline .mat-form-field-prefix,
  .mat-form-field-appearance-outline .mat-form-field-suffix {
    color: rgba(255, 255, 255, 0.7) !important;
  }

  .mat-icon {
    color: rgba(255, 255, 255, 0.7) !important;
  }

  /* Corregir welcome y crear-venta */
  .main-card,
  .datos-cliente,
  .datos-servicio {
    background-color: var(--sidenav-dark) !important;
    color: #ffffff !important;
    border-color: rgba(255, 255, 255, 0.1) !important;
  }

  .section-title,
  .mat-card-title,
  .mat-card-subtitle,
  .datos-cliente-content h2,
  .datos-servicio-content h2,
  .datos-cliente-content label,
  .datos-servicio-content label {
    color: #ffffff !important;
  }

  .mat-card-content {
    color: #ffffff !important;
  }

  .mat-form-field-label {
    color: rgba(255, 255, 255, 0.7) !important;
  }

  input.mat-input-element,
  textarea.mat-input-element {
    color: #ffffff !important;
  }

  .mat-select-value-text {
    color: #ffffff !important;
  }

  .mat-form-field-appearance-outline .mat-form-field-outline {
    color: rgba(255, 255, 255, 0.3) !important;
    box-shadow: none !important;
  }

  /* Estilos adicionales para welcome y crear-venta */
  .icon-container {
    background-color: var(--primary-dark) !important;
  }

  .icono-flotante {
    color: #ffffff !important;
  }

  .mat-checkbox-label {
    color: #ffffff !important;
  }

  .mat-checkbox-checked.mat-accent .mat-checkbox-background {
    background-color: var(--primary-dark) !important;
  }

  .mat-checkbox-frame {
    border-color: rgba(255, 255, 255, 0.5) !important;
  }

  .mat-radio-button .mat-radio-label-content {
    color: #ffffff !important;
  }

  .mat-radio-button.mat-accent .mat-radio-inner-circle {
    background-color: var(--primary-dark) !important;
  }

  .mat-radio-button.mat-accent.mat-radio-checked .mat-radio-outer-circle {
    border-color: var(--primary-dark) !important;
  }

  button.mat-raised-button {
    color: #ffffff !important;
  }

  button.mat-raised-button.mat-primary {
    background-color: var(--primary-dark) !important;
  }

  button.mat-raised-button.mat-warn {
    background-color: #f44336 !important;
  }

  button.mat-raised-button:hover {
    opacity: 0.9 !important;
  }

  button.mat-raised-button:disabled {
    background-color: rgba(255, 255, 255, 0.12) !important;
    color: rgba(255, 255, 255, 0.3) !important;
  }

  .mat-sidenav {
    background-color: var(--sidenav-dark) !important;
  }

  mat-sidenav-content {
    background-color: var(--background-dark) !important;
  }

  /* Header styles */
  .toolbar-custom {
    mat-icon {
      color: #ffffff !important;
    }

    .theme-toggle-btn {
      background-color: rgba(255, 255, 255, 0.1) !important;

      &:hover {
        background-color: rgba(255, 255, 255, 0.2) !important;
      }
    }

    /* Horario en el header */
    .horario-container {
      color: #ffffff !important;

      span {
        color: #ffffff !important;
      }

      .divider {
        color: rgba(255, 255, 255, 0.5) !important;
      }

      &:hover {
        background-color: rgba(255, 255, 255, 0.1) !important;
      }
    }
  }

  /* User name in header */
  .user-name {
    color: #ffffff !important;
  }

  /* Menu list styles - Usando Tailwind CSS */
  /* Los estilos del menú ahora se manejan con clases de Tailwind en el HTML */

  /* Estilos para el botón de cerrar sesión */
  .logout-container {
    border-top: none !important; /* Eliminar la línea blanca superior */

    button {
      background-color: #d32f2f !important;
      color: #ffffff !important;
    }
  }

  /* Estilos para el menú móvil */
  .mobile-menu {
    background-color: var(--sidenav-dark) !important;
    box-shadow: 0.125rem 0 0.375rem rgba(0, 0, 0, 0.2) !important;

    .mobile-menu-header {
      background-color: var(--sidenav-dark) !important;
      border-bottom: 0.0625rem solid rgba(255, 255, 255, 0.1) !important;
    }

    .mobile-nav-list {
      a.mat-list-item {
        color: #ffffff !important;

        mat-icon {
          color: #ffffff !important;
        }

        &:hover {
          background-color: rgba(255, 255, 255, 0.1) !important;
          color: #ffffff !important;

          mat-icon {
            color: #ffffff !important;
          }
        }

        &.active {
          background-color: rgba(255, 255, 255, 0.15) !important;
          color: #ffffff !important;

          mat-icon {
            color: #ffffff !important;
          }
        }
      }

      mat-expansion-panel {
        .mat-expansion-panel-header {
          &:hover {
            background-color: rgba(255, 255, 255, 0.1) !important;
          }

          mat-icon {
            color: #ffffff !important;
          }
        }
      }
    }

    .mobile-logout-container {
      border-top: 0.0625rem solid rgba(255, 255, 255, 0.1) !important;

      button {
        background-color: #d32f2f !important;
        color: #ffffff !important;
      }
    }
  }

  /* Button styles */
  .mat-raised-button:not([class*="mat-accent"]):not([class*="mat-warn"]) {
    background-color: var(--primary-dark) !important;
    color: #ffffff !important;
  }

  /* Botones de filtro y limpiar */
  button.mat-stroked-button[color="primary"] {
    color: #ffffff !important;
    border-color: rgba(255, 255, 255, 0.3) !important;

    &:hover {
      background-color: rgba(255, 255, 255, 0.1) !important;
    }

    mat-icon {
      color: #ffffff !important;
    }
  }

  button.mat-stroked-button[color="warn"] {
    color: #ff6b6b !important;
    border-color: rgba(255, 107, 107, 0.5) !important;

    &:hover {
      background-color: rgba(255, 107, 107, 0.1) !important;
    }

    mat-icon {
      color: #ff6b6b !important;
    }
  }

  /* Botones de acción en tablas */
  .action-button,
  .mat-cell button {
    color: #ffffff !important;

    mat-icon {
      color: #ffffff !important;
    }

    &:hover {
      background-color: rgba(255, 255, 255, 0.1) !important;
    }
  }

  /* Botones específicos en tablas */
  button.ver-detalles {
    color: #64b5f6 !important;
    border-color: rgba(100, 181, 246, 0.5) !important;

    mat-icon {
      color: #64b5f6 !important;
    }

    &:hover {
      background-color: rgba(100, 181, 246, 0.1) !important;
    }
  }

  button.editar {
    color: #4fc3f7 !important;
    border-color: rgba(79, 195, 247, 0.5) !important;

    mat-icon {
      color: #4fc3f7 !important;
    }

    &:hover {
      background-color: rgba(79, 195, 247, 0.1) !important;
    }
  }

  /* Botones de exportar */
  button.mat-stroked-button[color="accent"] {
    color: #81c784 !important;
    border-color: rgba(129, 199, 132, 0.5) !important;

    &:hover {
      background-color: rgba(129, 199, 132, 0.1) !important;
    }

    mat-icon {
      color: #81c784 !important;
    }
  }

  /* Form fields */
  .mat-form-field-label {
    color: rgba(255, 255, 255, 0.7) !important;
  }

  .mat-form-field-appearance-outline .mat-form-field-outline {
    color: rgba(255, 255, 255, 0.3) !important;
  }

  .mat-form-field-appearance-outline.mat-focused .mat-form-field-outline-thick {
    color: var(--primary-dark) !important;
  }

  .mat-form-field-appearance-outline .mat-form-field-infix {
    color: #ffffff !important;
  }

  .mat-input-element {
    color: #ffffff !important;
    caret-color: #ffffff !important;
  }

  .mat-select-value {
    color: #ffffff !important;
  }

  .mat-form-field-flex {
    background-color: rgba(255, 255, 255, 0.05) !important;
  }

  input.mat-input-element {
    color: #ffffff !important;
  }

  textarea.mat-input-element {
    color: #ffffff !important;
  }

  .mat-select-value-text {
    color: #ffffff !important;
  }

  .mat-select-arrow {
    color: rgba(255, 255, 255, 0.7) !important;
  }

  .mat-form-field-appearance-outline .mat-form-field-outline-start,
  .mat-form-field-appearance-outline .mat-form-field-outline-gap,
  .mat-form-field-appearance-outline .mat-form-field-outline-end {
    border-color: rgba(255, 255, 255, 0.3) !important;
  }

  .mat-select-arrow {
    color: #ffffff !important;
  }

  /* Estilos para los selectores en el tema oscuro */
  .mat-select-panel {
    background-color: var(--sidenav-dark) !important;
  }

  .mat-option {
    color: #ffffff !important;
  }

  .mat-option:hover:not(.mat-option-disabled) {
    background-color: rgba(255, 255, 255, 0.1) !important;
  }

  .mat-option.mat-selected:not(.mat-option-multiple):not(.mat-option-disabled) {
    background-color: rgba(30, 73, 118, 0.5) !important;
  }

  .mat-select-value-text {
    color: #ffffff !important;
  }

  .mat-select-arrow {
    color: rgba(255, 255, 255, 0.7) !important;
  }

  /* Estilos para los datepickers */
  .mat-calendar {
    background-color: var(--sidenav-dark) !important;
    color: #ffffff !important;
  }

  .mat-calendar-body-cell-content {
    color: #ffffff !important;
  }

  .mat-calendar-body-selected {
    background-color: #1e4976 !important;
    color: #ffffff !important;
  }

  .mat-calendar-body-today:not(.mat-calendar-body-selected):not(
      .mat-calendar-body-comparison-identical
    ) {
    border-color: rgba(255, 255, 255, 0.5) !important;
  }

  .mat-calendar-arrow {
    border-top-color: #ffffff !important;
  }

  .mat-datepicker-toggle {
    color: rgba(255, 255, 255, 0.7) !important;
  }

  .mat-datepicker-content {
    background-color: var(--sidenav-dark) !important;
    color: #ffffff !important;
  }

  .mat-calendar-previous-button,
  .mat-calendar-next-button {
    color: #ffffff !important;
  }

  .mat-calendar-table-header {
    color: rgba(255, 255, 255, 0.7) !important;
  }

  .mat-calendar-body-today:not(.mat-calendar-body-selected) {
    border-color: rgba(255, 255, 255, 0.5) !important;
  }

  .mat-calendar-arrow {
    border-top-color: #ffffff !important;
  }

  .mat-calendar-table-header,
  .mat-calendar-body-label {
    color: rgba(255, 255, 255, 0.7) !important;
  }

  .mat-datepicker-toggle-active {
    color: var(--primary-dark) !important;
  }

  /* Tables */
  .mat-table {
    background-color: var(--background-dark) !important;
  }

  .mat-header-cell,
  .mat-cell {
    color: #ffffff !important;
  }

  .mat-header-row {
    background-color: var(--sidenav-dark) !important;
  }

  .mat-row {
    border-bottom-color: rgba(255, 255, 255, 0.1) !important;
  }

  /* Hover en filas de tablas */
  tr.mat-row:hover {
    background-color: rgba(255, 255, 255, 0.05) !important;
  }

  /* Atributos en tablas */
  .mat-cell[_ngcontent],
  .mat-header-cell[_ngcontent],
  td.mat-cell,
  th.mat-header-cell {
    color: #ffffff !important;
  }

  /* Estilos para tablas modernas en modo oscuro */
  .modern-table-container {
    background-color: var(--background-dark) !important;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2) !important;
  }

  .modern-table {
    background-color: transparent !important;
  }

  .modern-table .mat-header-cell {
    background-color: #1e4976 !important;
    color: #ffffff !important;
    font-weight: bold !important;
    border-bottom-color: rgba(255, 255, 255, 0.1) !important;
  }

  .modern-table .mat-cell {
    color: #ffffff !important;
    border-bottom-color: rgba(255, 255, 255, 0.1) !important;
  }

  .modern-table .mat-row:hover {
    background-color: rgba(255, 255, 255, 0.05) !important;
  }

  /* Estados vacíos */
  .empty-state {
    background-color: var(--background-dark) !important;
    color: #ffffff !important;

    mat-icon {
      color: rgba(255, 255, 255, 0.5) !important;
    }

    p {
      color: rgba(255, 255, 255, 0.7) !important;
    }
  }

  /* Paginación */
  .pagination-controls {
    background-color: var(--background-dark) !important;
    border-top-color: rgba(255, 255, 255, 0.1) !important;

    span {
      color: #ffffff !important;
    }

    select {
      background-color: var(--sidenav-dark) !important;
      color: #ffffff !important;
      border-color: rgba(255, 255, 255, 0.2) !important;
    }

    option {
      background-color: var(--sidenav-dark) !important;
      color: #ffffff !important;
    }

    button {
      color: #ffffff !important;
      border-color: rgba(255, 255, 255, 0.3) !important;

      &:hover:not([disabled]) {
        background-color: rgba(255, 255, 255, 0.1) !important;
      }

      &[disabled] {
        color: rgba(255, 255, 255, 0.3) !important;
        border-color: rgba(255, 255, 255, 0.1) !important;
      }

      mat-icon {
        color: #ffffff !important;
      }
    }
  }

  /* Paginador de Angular Material */
  .mat-paginator {
    background-color: var(--background-dark) !important;
    color: #ffffff !important;
  }

  .mat-paginator-page-size-label,
  .mat-paginator-range-label {
    color: #ffffff !important;
  }

  .mat-paginator-navigation-previous,
  .mat-paginator-navigation-next,
  .mat-paginator-navigation-first,
  .mat-paginator-navigation-last {
    color: #ffffff !important;

    &[disabled] {
      color: rgba(255, 255, 255, 0.3) !important;
    }
  }

  .mat-paginator-container {
    color: #ffffff !important;
  }

  .mat-paginator-icon {
    fill: #ffffff !important;
  }

  /* Calendario */
  .fc-daygrid-day-number,
  .fc-col-header-cell-cushion,
  .fc-list-day-cushion,
  .fc-list-event-time,
  .fc-list-event-title,
  .fc-timegrid-axis-cushion,
  .fc-timegrid-slot-label-cushion,
  .fc-toolbar-title,
  .fc-daygrid-more-link {
    color: #ffffff !important;
  }

  .fc-daygrid-day.fc-day-today {
    background-color: rgba(255, 255, 255, 0.1) !important;
  }

  .fc-list-day {
    background-color: rgba(255, 255, 255, 0.05) !important;
  }

  .fc-list-event:hover td {
    background-color: rgba(255, 255, 255, 0.1) !important;
  }

  .fc-theme-standard .fc-list-day-cushion {
    background-color: rgba(255, 255, 255, 0.05) !important;
  }

  .fc-theme-standard td,
  .fc-theme-standard th,
  .fc-theme-standard .fc-scrollgrid {
    border-color: rgba(255, 255, 255, 0.1) !important;
  }

  /* Estilos para los inputs del modal de calendario */
  .calendar-modal input[type="text"],
  .calendar-modal input[type="date"],
  .calendar-modal input[type="time"],
  .calendar-modal textarea,
  .calendar-modal select {
    color: #ffffff !important;
    background-color: rgba(255, 255, 255, 0.05) !important;
    border-color: rgba(255, 255, 255, 0.2) !important;
  }

  /* Estilos para los botones del calendario */
  .fc-button-primary {
    background-color: var(--primary-dark) !important;
    border-color: var(--primary-dark) !important;
    color: #ffffff !important;
  }

  .fc-button-primary:hover {
    background-color: #1a3f66 !important;
    border-color: #1a3f66 !important;
  }

  .fc-button-primary:disabled {
    background-color: rgba(30, 73, 118, 0.7) !important;
    border-color: rgba(30, 73, 118, 0.7) !important;
  }

  .fc-button-primary:not(:disabled):active,
  .fc-button-primary:not(:disabled).fc-button-active {
    background-color: #153456 !important;
    border-color: #153456 !important;
  }

  .fc-daygrid-day-top {
    padding: 5px !important;
  }

  .fc-theme-standard .fc-scrollgrid,
  .fc-theme-standard td,
  .fc-theme-standard th {
    border-color: rgba(255, 255, 255, 0.2) !important;
  }

  .fc-toolbar-title {
    color: #ffffff !important;
  }

  .fc-button-primary {
    background-color: var(--primary-dark) !important;
    border-color: var(--primary-dark) !important;

    &:hover {
      background-color: rgba(255, 255, 255, 0.1) !important;
      border-color: rgba(255, 255, 255, 0.3) !important;
    }
  }

  .fc-button-primary:not(:disabled).fc-button-active,
  .fc-button-primary:not(:disabled):active {
    background-color: var(--primary-dark) !important;
    border-color: var(--primary-dark) !important;
  }

  /* Cards */
  .mat-card {
    background-color: var(--sidenav-dark) !important;
    color: #ffffff !important;
  }

  /* Estilos para ventas */
  .ventas-container {
    background-color: var(--background-dark) !important;
  }

  .ventas-container h1,
  .ventas-container h2,
  .ventas-container h3 {
    color: #ffffff !important;
  }

  .header-card,
  .table-card,
  .filter-card {
    background-color: var(--sidenav-dark) !important;
  }

  /* Estilos específicos para la tabla de ventas */
  .ventas-container .mat-table {
    background-color: transparent !important;
    color: #ffffff !important;

    .mat-header-cell {
      color: #ffffff !important;
      background-color: #1e4976 !important;
      font-weight: bold !important;
    }

    .mat-cell {
      color: #ffffff !important;
      border-bottom-color: rgba(255, 255, 255, 0.1) !important;
    }

    .mat-row:hover {
      background-color: rgba(255, 255, 255, 0.05) !important;
    }
  }

  /* Estilos para los botones de acción en ventas */
  .ventas-container .action-buttons button {
    color: #ffffff !important;
    border-color: rgba(255, 255, 255, 0.3) !important;

    &:hover {
      background-color: rgba(255, 255, 255, 0.1) !important;
    }

    &.mat-primary {
      background-color: var(--primary-dark) !important;
      color: #ffffff !important;
    }

    &.mat-accent {
      background-color: #1976d2 !important;
      color: #ffffff !important;
    }

    &.mat-warn {
      background-color: #c62828 !important;
      color: #ffffff !important;
    }

    mat-icon {
      color: #ffffff !important;
    }
  }

  /* Estilos específicos para los botones de acción en la tabla */
  .ventas-container .mat-table .action-buttons button {
    color: #ffffff !important;
    border-color: rgba(255, 255, 255, 0.3) !important;

    &.mat-stroked-button {
      border-color: rgba(255, 255, 255, 0.3) !important;
    }

    mat-icon {
      color: #ffffff !important;
    }
  }

  /* Estilos para los modales de ventas */
  .ventas-container .modal-overlay {
    background-color: rgba(0, 0, 0, 0.7) !important;
  }

  .ventas-container .modal-content {
    background-color: var(--sidenav-dark) !important;
    color: #ffffff !important;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.5) !important;
  }

  .ventas-container .modal-header {
    background-color: #1e4976 !important;
    border-bottom-color: rgba(255, 255, 255, 0.1) !important;
    color: #ffffff !important;

    h2,
    h3 {
      color: #ffffff !important;
    }

    button mat-icon {
      color: #ffffff !important;
    }
  }

  .ventas-container .modal-body {
    background-color: var(--sidenav-dark) !important;
    color: #ffffff !important;

    .form-header {
      color: #ffffff !important;
      border-bottom-color: rgba(255, 255, 255, 0.1) !important;
      background-color: #1e4976 !important;
    }

    .form-section {
      background-color: rgba(255, 255, 255, 0.05) !important;
      border-color: rgba(255, 255, 255, 0.1) !important;
    }

    .form-label {
      color: rgba(255, 255, 255, 0.9) !important;
      font-weight: bold !important;
    }

    .form-value {
      color: #ffffff !important;
      border-left-color: rgba(255, 255, 255, 0.1) !important;
    }

    .form-row {
      border-bottom-color: rgba(255, 255, 255, 0.1) !important;
    }
  }

  .ventas-container .modal-footer {
    border-top-color: rgba(255, 255, 255, 0.1) !important;
    background-color: var(--sidenav-dark) !important;
  }

  /* Estilos para los botones principales en ventas */
  .ventas-container .header-buttons button {
    color: #ffffff !important;

    &.mat-primary {
      background-color: var(--primary-dark) !important;
      color: #ffffff !important;
    }

    &.mat-accent {
      background-color: #1976d2 !important;
      color: #ffffff !important;
    }

    mat-icon {
      color: #ffffff !important;
    }
  }

  /* Estilos para los botones de estado en ventas */
  .ventas-container .estado-button {
    color: #ffffff !important;
  }

  /* Estilos para crear venta */
  .crear-venta-container {
    background-color: var(--background-dark) !important;
  }

  .stepper-container {
    background-color: var(--sidenav-dark) !important;
  }

  /* Estilos específicos para crear-venta */
  .crear-venta-container,
  .form-section {
    background-color: var(--background-dark) !important;
  }

  .crear-venta-container h1,
  .crear-venta-container h2,
  .crear-venta-container h3,
  .form-section h1,
  .form-section h2,
  .form-section h3 {
    color: #ffffff !important;
  }

  .datos-cliente-residencial,
  .datos-cliente-empresa,
  .datos-promocion-container,
  .datos-cliente-container {
    background-color: var(--sidenav-dark) !important;
    color: #ffffff !important;
    border-color: rgba(255, 255, 255, 0.1) !important;
  }

  .datos-del-cliente,
  .datos-del-servicio,
  .section-title {
    color: #ffffff !important;
    font-weight: bold !important;
    background-color: #1e4976 !important;
    padding: 8px 12px !important;
    border-radius: 4px !important;
  }

  .direccion-container {
    background-color: var(--sidenav-dark) !important;
    color: #ffffff !important;
    border-color: rgba(255, 255, 255, 0.1) !important;
  }

  .tipo-cliente-selector .mat-card {
    background-color: var(--sidenav-dark) !important;
    color: #ffffff !important;
  }

  .radio-group-container {
    background-color: var(--sidenav-dark) !important;

    .radio-group-label {
      color: #ffffff !important;
    }
  }

  .mat-radio-button .mat-radio-label-content {
    color: #ffffff !important;
  }

  .add-direccion-button-container button,
  .footer-section button {
    color: #ffffff !important;

    &.mat-primary {
      background-color: #1e4976 !important;
    }

    &.mat-warn {
      background-color: #c62828 !important;
    }

    mat-icon {
      color: #ffffff !important;
    }
  }

  /* Estilos para los campos de formulario en crear ventas */
  .crear-venta-container
    .mat-form-field-appearance-outline
    .mat-form-field-outline,
  .form-section .mat-form-field-appearance-outline .mat-form-field-outline {
    color: rgba(255, 255, 255, 0.3) !important;
    box-shadow: none !important;
  }

  .crear-venta-container
    .mat-form-field-appearance-outline
    .mat-form-field-outline-thick,
  .form-section
    .mat-form-field-appearance-outline
    .mat-form-field-outline-thick {
    color: #1e4976 !important;
    box-shadow: none !important;
  }

  .crear-venta-container
    .mat-form-field-appearance-outline
    .mat-form-field-label,
  .form-section .mat-form-field-appearance-outline .mat-form-field-label {
    color: rgba(255, 255, 255, 0.7) !important;
  }

  .crear-venta-container
    .mat-form-field-appearance-outline.mat-focused
    .mat-form-field-label,
  .form-section
    .mat-form-field-appearance-outline.mat-focused
    .mat-form-field-label {
    color: #1e4976 !important;
  }

  .crear-venta-container .mat-input-element,
  .crear-venta-container .mat-select-value-text,
  .form-section .mat-input-element,
  .form-section .mat-select-value-text {
    color: #ffffff !important;
  }

  .crear-venta-container .mat-select-arrow,
  .form-section .mat-select-arrow {
    color: rgba(255, 255, 255, 0.7) !important;
  }

  .crear-venta-container .mat-datepicker-toggle,
  .form-section .mat-datepicker-toggle {
    color: rgba(255, 255, 255, 0.7) !important;
  }

  .crear-venta-container input[type="date"],
  .crear-venta-container input[type="time"],
  .form-section input[type="date"],
  .form-section input[type="time"] {
    color: #ffffff !important;
    background-color: rgba(255, 255, 255, 0.05) !important;
    border-color: rgba(255, 255, 255, 0.2) !important;

    &::-webkit-calendar-picker-indicator {
      filter: invert(1) !important;
    }
  }

  /* Estilos para los estados de ventas */
  .estado-valido {
    background-color: #2e7d32 !important;
    color: white !important;
  }

  .estado-proceso {
    background-color: #f57c00 !important;
    color: white !important;
  }

  .estado-no-valido {
    background-color: #c62828 !important;
    color: white !important;
  }

  /* Estilos para los campos de formulario en ventas */
  .ventas-container .mat-form-field-appearance-outline .mat-form-field-outline,
  .crear-venta-container
    .mat-form-field-appearance-outline
    .mat-form-field-outline {
    color: rgba(255, 255, 255, 0.3) !important;
  }

  .ventas-container .mat-form-field-appearance-outline .mat-form-field-label,
  .crear-venta-container
    .mat-form-field-appearance-outline
    .mat-form-field-label {
    color: rgba(255, 255, 255, 0.7) !important;
  }

  .ventas-container
    .mat-form-field-appearance-outline.mat-focused
    .mat-form-field-outline-thick,
  .crear-venta-container
    .mat-form-field-appearance-outline.mat-focused
    .mat-form-field-outline-thick {
    color: var(--primary-dark) !important;
  }

  .add-direccion-button-container button,
  .footer-section button {
    color: #ffffff !important;
  }

  .add-direccion-button-container button.mat-primary,
  .footer-section button.mat-primary {
    background-color: var(--primary-dark) !important;
  }

  .add-direccion-button-container button.mat-warn,
  .footer-section button.mat-warn {
    background-color: #f44336 !important;
  }

  /* Estilos para los datos del cliente residencial */
  .datos-cliente-residencial {
    color: #ffffff !important;
  }

  .datos-informacion {
    background-color: var(--sidenav-dark) !important;
    color: #ffffff !important;
  }

  .datos-informacion-content {
    color: #ffffff !important;
  }

  .datos-informacion-content .info-grid {
    color: #ffffff !important;
  }

  .datos-informacion-content .info-grid .info-item {
    color: #ffffff !important;
  }

  .datos-informacion-content .info-grid .info-item .info-label {
    color: rgba(255, 255, 255, 0.7) !important;
  }

  .datos-informacion-content .info-grid .info-item .info-value {
    color: #ffffff !important;
  }

  .direccion-container {
    background-color: var(--sidenav-dark) !important;
    color: #ffffff !important;
  }

  .direccion-container h3 {
    color: #ffffff !important;
  }

  /* Estilos adicionales para crear ventas */
  .crear-venta-container .mat-card,
  .form-section .main-card {
    background-color: var(--sidenav-dark) !important;
    color: #ffffff !important;
  }

  .crear-venta-container .mat-card-title,
  .crear-venta-container .mat-card-subtitle,
  .crear-venta-container .section-title,
  .crear-venta-container label,
  .crear-venta-container h2,
  .crear-venta-container h3,
  .crear-venta-container h4,
  .form-section .section-title,
  .form-section label,
  .form-section h2,
  .form-section h3,
  .form-section h4,
  .form-section .mat-card-title,
  .form-section .mat-card-subtitle {
    color: #ffffff !important;
  }

  .crear-venta-container .mat-form-field-label,
  .form-section .mat-form-field-label {
    color: rgba(255, 255, 255, 0.7) !important;
  }

  .crear-venta-container .mat-form-field-underline,
  .form-section .mat-form-field-underline {
    background-color: rgba(255, 255, 255, 0.3) !important;
  }

  .crear-venta-container .mat-form-field-ripple,
  .form-section .mat-form-field-ripple {
    background-color: var(--primary-dark) !important;
  }

  .crear-venta-container .mat-radio-button .mat-radio-label-content {
    color: #ffffff !important;
  }

  .crear-venta-container .mat-radio-button.mat-accent .mat-radio-inner-circle {
    background-color: var(--primary-dark) !important;
  }

  .crear-venta-container
    .mat-radio-button.mat-accent.mat-radio-checked
    .mat-radio-outer-circle {
    border-color: var(--primary-dark) !important;
  }

  .crear-venta-container .mat-checkbox-label {
    color: #ffffff !important;
  }

  .crear-venta-container
    .mat-checkbox-checked.mat-accent
    .mat-checkbox-background {
    background-color: var(--primary-dark) !important;
  }

  .crear-venta-container .mat-checkbox-frame {
    border-color: rgba(255, 255, 255, 0.5) !important;
  }

  .crear-venta-container
    .mat-form-field-appearance-outline
    .mat-form-field-outline,
  .form-section .mat-form-field-appearance-outline .mat-form-field-outline {
    color: rgba(255, 255, 255, 0.3) !important;
    box-shadow: none !important;
  }

  .crear-venta-container
    .mat-form-field-appearance-outline.mat-focused
    .mat-form-field-outline-thick,
  .form-section
    .mat-form-field-appearance-outline.mat-focused
    .mat-form-field-outline-thick {
    color: var(--primary-dark) !important;
  }

  .crear-venta-container
    .mat-form-field-appearance-outline
    .mat-form-field-infix,
  .form-section .mat-form-field-appearance-outline .mat-form-field-infix {
    padding: 0.5em 0 1em 0 !important;
  }

  .crear-venta-container
    .mat-form-field-appearance-outline
    .mat-form-field-flex,
  .form-section .mat-form-field-appearance-outline .mat-form-field-flex {
    padding: 0 0.75em 0 0.75em !important;
    margin-top: -0.25em !important;
  }

  .crear-venta-container .mat-form-field-subscript-wrapper,
  .form-section .mat-form-field-subscript-wrapper {
    margin-top: 0.5em !important;
  }

  .crear-venta-container
    .mat-form-field-appearance-outline
    .mat-form-field-label,
  .form-section .mat-form-field-appearance-outline .mat-form-field-label {
    top: 1.5em !important;
    margin-top: -0.25em !important;
  }

  .crear-venta-container
    .mat-form-field-appearance-outline.mat-form-field-can-float.mat-form-field-should-float
    .mat-form-field-label,
  .form-section
    .mat-form-field-appearance-outline.mat-form-field-can-float.mat-form-field-should-float
    .mat-form-field-label {
    transform: translateY(-1.1em) scale(0.75) !important;
  }

  /* Estilos para los campos de texto en crear ventas */
  .crear-venta-container input.mat-input-element,
  .form-section input.mat-input-element {
    color: #ffffff !important;
  }

  .crear-venta-container textarea.mat-input-element,
  .form-section textarea.mat-input-element {
    color: #ffffff !important;
  }

  .crear-venta-container .mat-select-value-text,
  .form-section .mat-select-value-text {
    color: #ffffff !important;
  }

  .crear-venta-container .mat-select-arrow,
  .form-section .mat-select-arrow,
  .ventas-container .mat-select-arrow {
    color: rgba(255, 255, 255, 0.7) !important;
  }

  .crear-venta-container
    .mat-form-field-appearance-outline
    .mat-select-arrow-wrapper,
  .ventas-container
    .mat-form-field-appearance-outline
    .mat-select-arrow-wrapper {
    transform: translateY(0) !important;
  }

  .crear-venta-container .mat-datepicker-toggle,
  .ventas-container .mat-datepicker-toggle {
    color: rgba(255, 255, 255, 0.7) !important;
  }

  .crear-venta-container .mat-datepicker-toggle-active,
  .ventas-container .mat-datepicker-toggle-active {
    color: var(--primary-dark) !important;
  }

  /* Estilos para los botones en crear ventas */
  .crear-venta-container .add-direccion-button-container button,
  .crear-venta-container .footer-section button,
  .form-section button.mat-raised-button,
  .form-section button.mat-button,
  .ventas-container button.mat-raised-button,
  .ventas-container button.mat-button {
    color: #ffffff !important;
  }

  /* Estilos para los botones de acción en la tabla de ventas */
  .ventas-container .mat-icon-button {
    color: #ffffff !important;
  }

  /* Estilos para el paginador en ventas */
  .ventas-container .mat-paginator {
    background-color: transparent !important;
    color: #ffffff !important;
  }

  .ventas-container .mat-paginator-page-size-label,
  .ventas-container .mat-paginator-range-label {
    color: #ffffff !important;
  }

  /* Estilos para los filtros en ventas */
  .ventas-container .filter-section {
    background-color: rgba(30, 73, 118, 0.2) !important;
    border-color: rgba(255, 255, 255, 0.1) !important;
  }

  /* Estilos para los chips y badges en ventas */
  .ventas-container .mat-chip {
    background-color: rgba(30, 73, 118, 0.5) !important;
    color: #ffffff !important;
  }

  .crear-venta-container .add-direccion-button-container button.mat-primary,
  .crear-venta-container .footer-section button.mat-primary,
  .form-section button.mat-raised-button.mat-primary,
  .form-section button.mat-button.mat-primary {
    background-color: var(--primary-dark) !important;
  }

  .crear-venta-container .add-direccion-button-container button.mat-warn,
  .crear-venta-container .footer-section button.mat-warn,
  .form-section button.mat-raised-button.mat-warn,
  .form-section button.mat-button.mat-warn {
    background-color: #f44336 !important;
  }

  .crear-venta-container .add-direccion-button-container button:hover,
  .crear-venta-container .footer-section button:hover,
  .form-section button.mat-raised-button:hover,
  .form-section button.mat-button:hover {
    opacity: 0.9 !important;
  }

  .crear-venta-container .add-direccion-button-container button:disabled,
  .crear-venta-container .footer-section button:disabled,
  .form-section button.mat-raised-button:disabled,
  .form-section button.mat-button:disabled {
    background-color: rgba(255, 255, 255, 0.12) !important;
    color: rgba(255, 255, 255, 0.3) !important;
  }

  /* Estilos adicionales para welcome */
  .form-section .icon-container {
    background-color: var(--primary-dark) !important;
  }

  .form-section .icono-flotante {
    color: #ffffff !important;
  }

  .form-section .mat-checkbox-label {
    color: #ffffff !important;
  }

  .form-section .mat-checkbox-checked.mat-accent .mat-checkbox-background {
    background-color: var(--primary-dark) !important;
  }

  .form-section .mat-checkbox-frame {
    border-color: rgba(255, 255, 255, 0.5) !important;
  }

  .form-section .mat-radio-button .mat-radio-label-content {
    color: #ffffff !important;
  }

  .form-section .mat-radio-button.mat-accent .mat-radio-inner-circle {
    background-color: var(--primary-dark) !important;
  }

  .form-section
    .mat-radio-button.mat-accent.mat-radio-checked
    .mat-radio-outer-circle {
    border-color: var(--primary-dark) !important;
  }

  .mat-step-header .mat-step-label,
  .mat-step-header .mat-step-optional {
    color: rgba(255, 255, 255, 0.7) !important;
  }

  .mat-step-header .mat-step-label.mat-step-label-active {
    color: #ffffff !important;
  }

  .mat-step-header .mat-step-icon {
    background-color: var(--primary-dark) !important;
    color: #ffffff !important;
  }

  .mat-step-header .mat-step-icon-selected {
    background-color: var(--primary-dark) !important;
  }

  .mat-step-header .mat-step-icon-state-done {
    background-color: #4caf50 !important;
    color: #ffffff !important;
  }

  .mat-step-header .mat-step-icon-state-edit {
    background-color: #ff9800 !important;
    color: #ffffff !important;
  }

  /* Home page buttons */
  .menu-button {
    background-color: rgba(255, 255, 255, 0.1) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;

    &:hover {
      background-color: rgba(255, 255, 255, 0.15) !important;
    }
  }

  /* Home page background */
  .contenedor-imagen-home {
    background-color: var(--background-dark) !important;
  }

  /* Announcements section */
  .anuncios-section,
  .listado-anuncios {
    background-color: var(--background-dark) !important;
  }

  /* Estilos para anuncios */
  .anuncios-title,
  .anuncios-recientes .header-container h2 {
    color: #ffffff !important;
  }

  .anuncios-grid .anuncio-card {
    background-color: var(--sidenav-dark) !important;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.3) !important;
  }

  .anuncio-card .card-title,
  .anuncio-card .card-description {
    color: #ffffff !important;
  }

  .anuncio-card:hover {
    box-shadow: 0 0.75rem 1.5rem rgba(0, 0, 0, 0.4) !important;
  }

  /* Estilos para el listado de anuncios */
  .anuncios-list-container {
    background-color: var(--background-dark) !important;
  }

  .anuncios-list-header h1 {
    color: #ffffff !important;
  }

  .search-bar {
    background-color: var(--sidenav-dark) !important;
    border-color: rgba(255, 255, 255, 0.2) !important;
  }

  .search-bar input {
    color: #ffffff !important;
  }

  .search-bar mat-icon {
    color: rgba(255, 255, 255, 0.7) !important;
  }

  /* Preserve original styles for specific components */
  .menu-button svg,
  .menu-button-container svg {
    /* Keep original SVG styles */
    fill: currentColor !important;
  }

  /* Ajustes para los íconos de Material en el menú */
  mat-nav-list mat-icon,
  .mat-expansion-panel-header mat-icon {
    color: #ffffff !important;
  }

  /* Ajustes adicionales para el menú */
  .mat-drawer-inner-container {
    background-color: var(--sidenav-dark) !important;
  }

  .mat-drawer {
    background-color: var(--sidenav-dark) !important;
    border-right: none !important; /* Eliminar la línea blanca entre el menú y el contenido */
  }

  .mat-drawer-content {
    background-color: var(--background-dark) !important;
  }

  /* Eliminar bordes blancos en el sidenav */
  .mat-drawer-side {
    border-right: none !important;
  }

  /* Eliminar bordes blancos adicionales */
  .mat-list-base {
    border: none !important;
  }

  .mat-list-item {
    border: none !important;
  }

  .mat-list-item-ripple {
    border: none !important;
  }

  /* Preserve sales section styles */
  .ventas-container .mat-raised-button,
  .ventas-container .mat-icon-button {
    /* Keep original button styles in sales section */
    background-color: inherit !important;
    color: inherit !important;
  }
}

.fullscreen-dialog {
  max-width: 100vw !important;
  max-height: 100vh !important;
  width: 100vw !important;
  height: 100vh !important;

  .mat-dialog-container {
    padding: 0;
    border-radius: 0;
    background: transparent;
    box-shadow: none;
    overflow: hidden;
  }
}

/* Estilos para los MatDialog en tema azulino */
body.dark-theme {
  .modern-modal .mat-dialog-container {
    background-color: var(--sidenav-dark) !important;
    color: #ffffff !important;
  }

  .modern-modal .mat-dialog-title {
    background-color: #0a1628 !important;
    color: #ffffff !important;
    border-bottom-color: rgba(255, 255, 255, 0.1) !important;
  }

  .modern-modal .mat-dialog-content {
    color: #ffffff !important;
  }

  .modern-modal .mat-dialog-actions {
    border-top: 1px solid rgba(255, 255, 255, 0.1) !important;
  }

  /* Estilos para los formularios dentro de diálogos */
  .modern-modal .mat-form-field-label {
    color: rgba(255, 255, 255, 0.7) !important;
  }

  .modern-modal .mat-form-field-appearance-outline .mat-form-field-outline {
    color: rgba(255, 255, 255, 0.3) !important;
  }

  .modern-modal
    .mat-form-field-appearance-outline.mat-focused
    .mat-form-field-outline-thick {
    color: var(--primary-dark) !important;
  }

  .mat-dialog-container {
    background-color: var(--sidenav-dark) !important;
    color: #ffffff !important;
  }

  .mat-dialog-title {
    color: #ffffff !important;
  }

  .mat-dialog-content {
    color: #ffffff !important;
  }

  .mat-dialog-actions {
    border-top: 1px solid rgba(255, 255, 255, 0.1) !important;
  }

  /* Estilos para los formularios dentro de diálogos */
  .mat-dialog-container .mat-form-field-label {
    color: rgba(255, 255, 255, 0.7) !important;
  }

  .mat-dialog-container .mat-form-field-underline {
    background-color: rgba(255, 255, 255, 0.3) !important;
  }

  .mat-dialog-container .mat-form-field-ripple {
    background-color: var(--primary-dark) !important;
  }

  /* Estilos para los modales de asignación de asesores */
  .modal-overlay {
    background-color: rgba(0, 0, 0, 0.7) !important;
  }

  .modal-content {
    background-color: var(--sidenav-dark) !important;
    color: #ffffff !important;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.5) !important;
  }

  .modal-header {
    border-bottom-color: rgba(255, 255, 255, 0.1) !important;

    h3 {
      color: #ffffff !important;
    }

    .close,
    button mat-icon {
      color: #ffffff !important;
    }
  }

  .modal-body {
    .mat-form-field-appearance-outline .mat-form-field-outline {
      color: rgba(255, 255, 255, 0.3) !important;
    }

    .mat-form-field-label {
      color: rgba(255, 255, 255, 0.7) !important;
    }

    .mat-input-element {
      color: #ffffff !important;
    }

    input[type="text"],
    input[type="email"],
    input[type="number"],
    input[type="search"],
    input[type="password"],
    input[type="tel"],
    input[type="url"],
    textarea,
    select {
      color: #ffffff !important;
      background-color: rgba(255, 255, 255, 0.05) !important;
      border-color: rgba(255, 255, 255, 0.2) !important;

      &::placeholder {
        color: rgba(255, 255, 255, 0.5) !important;
      }
    }

    .mat-table {
      background-color: transparent !important;

      .mat-header-cell {
        color: rgba(255, 255, 255, 0.7) !important;
        background-color: rgba(255, 255, 255, 0.05) !important;
      }

      .mat-cell {
        color: #ffffff !important;
        border-bottom-color: rgba(255, 255, 255, 0.1) !important;
      }

      .mat-row:hover {
        background-color: rgba(255, 255, 255, 0.05) !important;
      }
    }

    button.mat-stroked-button {
      color: #ffffff !important;
      border-color: rgba(255, 255, 255, 0.3) !important;

      &:hover {
        background-color: rgba(255, 255, 255, 0.05) !important;
      }
    }

    .mat-checkbox-frame {
      border-color: rgba(255, 255, 255, 0.5) !important;
    }

    .mat-checkbox-checked .mat-checkbox-background {
      background-color: var(--primary-dark) !important;
    }
  }

  .modal-footer {
    border-top-color: rgba(255, 255, 255, 0.1) !important;
  }

  /* Estilos para los inputs de fecha y hora */
  input[type="date"],
  input[type="time"] {
    color: #ffffff !important;
    background-color: rgba(255, 255, 255, 0.05) !important;
    border-color: rgba(255, 255, 255, 0.2) !important;

    &::-webkit-calendar-picker-indicator {
      filter: invert(1) !important;
    }
  }

  .mat-dialog-container .mat-input-element {
    color: #ffffff !important;
  }

  .mat-dialog-container
    .mat-form-field-appearance-outline
    .mat-form-field-outline {
    color: rgba(255, 255, 255, 0.3) !important;
  }

  .mat-dialog-container
    .mat-form-field-appearance-outline.mat-focused
    .mat-form-field-outline-thick {
    color: var(--primary-dark) !important;
  }

  /* Estilos para los botones en diálogos */
  .mat-dialog-container .mat-button {
    color: rgba(255, 255, 255, 0.7) !important;
  }

  .mat-dialog-container .mat-button:hover {
    background-color: rgba(255, 255, 255, 0.1) !important;
  }

  .mat-dialog-container .mat-raised-button.mat-primary {
    background-color: var(--primary-dark) !important;
    color: #ffffff !important;
  }

  .mat-dialog-container .mat-raised-button.mat-accent {
    background-color: #26afe5 !important;
    color: #ffffff !important;
  }

  .mat-dialog-container .mat-raised-button.mat-warn {
    background-color: #f44336 !important;
    color: #ffffff !important;
  }

  /* Estilos para diálogos específicos */
  .files-upload-dialog .mat-dialog-container,
  .confirm-dialog .mat-dialog-container,
  .export-dialog .mat-dialog-container {
    background-color: var(--sidenav-dark) !important;
    color: #ffffff !important;
  }
}

.mat-raised-button {
  font-size: 0.9rem !important;
}
.mat-list-base {
  padding: 0px 1rem !important;
}
.mat-icon {
  width: 1.5rem !important;
  height: 1.5rem !important;
}
.mat-list-base .mat-list-item {
  height: 3rem !important;
  font-size: 1rem !important;
}
.material-icons {
  font-size: 1.5rem !important;
}
.mat-raised-button {
  min-width: 4rem !important;
  line-height: 3.25rem !important;
  padding: 0 1rem !important;
  border-radius: 0.25rem !important;
}
.mat-form-field-outline-start {
  width: 0.34375rem !important;
}
.mat-form-field-outline-gap {
  width: 6.296875rem !important;
}
.mat-form-field-outline-end {
  min-width: 0.3125rem !important;
}
.mat-toolbar-row,
.mat-toolbar-single-row {
  height: 4rem;
}
.mat-toolbar {
  font: 500 1.25rem / 2rem Roboto, "Helvetica Neue", sans-serif;
}
.mat-stroked-button {
  border: 0.0625rem solid currentColor !important;
  padding: 0 0.95rem !important;
  line-height: 2.125rem !important;
  font-size: 0.9rem;
}
.mat-body-2 {
  font: 500 0.9rem / 1.5rem Roboto, "Helvetica Neue", sans-serif;
}

@media screen and (max-resolution: 96dpi) {
  html {
    font-size: 16px !important; /* 1rem = 16px */
  }
}

/* 110% scaling (~105dpi) */
@media screen and (min-resolution: 105dpi) and (max-resolution: 110dpi) {
  html {
    font-size: 14.5px !important;
  }
}

/* 125% scaling (~120dpi) */
@media screen and (min-resolution: 120dpi) and (max-resolution: 143dpi) {
  html {
    font-size: 12.8px !important;
  }
}

/* 150% scaling (~144dpi) */
@media screen and (min-resolution: 144dpi) and (max-resolution: 159dpi) {
  html {
    font-size: 10.67px !important;
  }
}

/* 175% scaling (~168dpi) */
@media screen and (min-resolution: 160dpi) and (max-resolution: 191dpi) {
  html {
    font-size: 9.14px !important;
  }
}

/* 200% scaling (~192dpi) */
@media screen and (min-resolution: 192dpi) and (max-resolution: 239dpi) {
  html {
    font-size: 8px !important;
  }
}

/* 225% scaling (~216dpi) */
@media screen and (min-resolution: 240dpi) and (max-resolution: 263dpi) {
  html {
    font-size: 7.11px !important;
  }
}

/* 250% scaling (~240dpi) */
@media screen and (min-resolution: 264dpi) and (max-resolution: 287dpi) {
  html {
    font-size: 6.4px !important;
  }
}

/* 300% scaling (~288dpi) */
@media screen and (min-resolution: 288dpi) and (max-resolution: 335dpi) {
  html {
    font-size: 5.33px !important;
  }
}

/* 350% scaling (~336dpi) */
@media screen and (min-resolution: 336dpi) and (max-resolution: 383dpi) {
  html {
    font-size: 4.57px !important;
  }
}

/* 400% scaling (~384dpi) */
@media screen and (min-resolution: 384dpi) and (max-resolution: 431dpi) {
  html {
    font-size: 4px !important;
  }
}

/* 450% scaling (~432dpi) */
@media screen and (min-resolution: 432dpi) and (max-resolution: 479dpi) {
  html {
    font-size: 3.56px !important;
  }
}

/* 500% scaling (~480dpi) */
@media screen and (min-resolution: 480dpi) {
  html {
    font-size: 3.2px !important;
  }
}

/* Ajustes para pantallas grandes (3840px x 2160px) */
@media screen and (min-width: 3840px) {
  html {
    font-size: 17px !important; /* Aumentar el tamaño de fuente para pantallas 4K */
  }
}

/* Para pantallas más pequeñas */
@media screen and (max-width: 768px) {
  html {
    font-size: 14px !important; /* Ajuste para pantallas medianas (tabletas) */
  }
}

/* Estilos para el menú de Tipificaciones en modo oscuro */
.darkmode
  .tipificaciones-panel
  .mat-expansion-panel-body
  .mat-list-item-content {
  padding: 2rem 0 !important;
  height: auto !important;
  min-height: 4rem !important;
  margin-top: 20px !important;
  margin-bottom: 20px !important;
}

/* Estilo directo para todos los elementos del menú en modo oscuro */
.darkmode .mat-expansion-panel-body a.mat-list-item {
  margin-top: 20px !important;
  margin-bottom: 20px !important;
}

/* Estilo directo para el contenido de los elementos de lista en modo oscuro */
.darkmode .mat-list-item-content {
  margin-top: 20px !important;
  margin-bottom: 20px !important;
}

/* Estilo específico para los elementos de Tipificaciones en modo oscuro */
.darkmode .tipificaciones-item {
  margin-top: 30px !important;
  margin-bottom: 30px !important;
  display: block !important;
  height: auto !important;
}

.darkmode .tipificaciones-item .mat-list-item-content {
  margin-top: 30px !important;
  margin-bottom: 30px !important;
  padding: 30px 0 !important;
  height: auto !important;
  min-height: 5rem !important;
}

.darkmode .tipificaciones-panel .mat-expansion-panel-body a.mat-list-item {
  margin: 3rem 0 !important;
  display: block !important;
  height: auto !important;
}

.darkmode .tipificaciones-panel .mat-expansion-panel-body {
  padding: 3rem 0 !important;
}

.darkmode
  .tipificaciones-panel
  .mat-expansion-panel-body
  .navegacion-list-label {
  font-size: 1rem !important;
  line-height: 1.8 !important;
  padding: 0.5rem 0 !important;
}

.darkmode .tipificaciones-panel .mat-expansion-panel-body mat-icon {
  font-size: 1.5rem !important;
  margin-right: 1rem !important;
}

html,
body {
  height: 100%;
  width: 100%;
  margin: 0;
  padding: 0;
}
