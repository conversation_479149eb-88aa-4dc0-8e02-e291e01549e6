<div class="w-full min-h-[calc(100vh-64px)] bg-slate-50 dark:bg-slate-900">
  <!-- Cabecera con diseño moderno y minimalista -->
  <div class="bg-blue-600 dark:bg-blue-800">
    <div class="max-w-screen-xl mx-auto py-8 px-4 text-center">
      <h1 class="text-2xl font-bold text-white mb-2">
        Centro de Soporte Técnico
      </h1>
      <p class="text-blue-100 text-lg">¿En qué podemos ayudarlo hoy?</p>
    </div>
  </div>

  <!-- Contenido principal con diseño de tarjetas moderno -->
  <div class="max-w-screen-xl mx-auto px-4 py-6">
    <!-- Barra de herramientas con diseño limpio -->
    <div class="flex flex-wrap justify-between items-center mb-4">
      <div class="flex items-center space-x-2 mb-2 sm:mb-0">
        <span class="text-slate-600 dark:text-slate-300 text-sm">Mostrar</span>
        <select
          class="form-select py-1.5 px-3 bg-white border border-slate-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500/40 focus:border-blue-500 dark:bg-slate-800 dark:border-slate-600 dark:text-white"
          [(ngModel)]="pagination.value.perPage"
          (change)="apiFaqListPagination()"
        >
          <option *ngFor="let count of countElements" [ngValue]="count">
            {{ count }}
          </option>
        </select>
        <span class="text-slate-600 dark:text-slate-300 text-sm"
          >registros</span
        >
      </div>

      <button
        class="inline-flex items-center py-2 px-4 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:ring-offset-2 dark:bg-blue-700 dark:hover:bg-blue-600"
        (click)="openModalForm()"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-4 w-4 mr-1.5"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M12 4v16m8-8H4"
          />
        </svg>
        Agregar nuevo
      </button>
    </div>

    <!-- Contenedor de tarjetas con sombra suave -->
    <div
      class="bg-white dark:bg-slate-800 rounded-lg shadow-sm overflow-hidden"
    >
      <!-- Loader con diseño mejorado -->
      <div *ngIf="loading$ | async" class="p-6">
        <app-placeholder-card
          [rows]="pagination.value.perPage ?? 10"
        ></app-placeholder-card>
      </div>

      <!-- Listado de FAQs con diseño de tarjetas mejorado -->
      <div
        *ngIf="!(loading$ | async)"
        class="divide-y divide-slate-100 dark:divide-slate-700"
      >
        <div
          class="p-5 hover:bg-slate-50 dark:hover:bg-slate-700/30 transition-colors"
          *ngFor="let faq of paginationResult.data"
        >
          <!-- Estilos personalizados para el componente card-v1 -->
          <style>
            /* Estilos para el estado abierto/cerrado que funcionan en modo claro y oscuro */
            .faq-card-estado {
              display: inline-block;
              padding: 0.25rem 0.5rem;
              border-radius: 4px;
              font-weight: 500;
              white-space: nowrap;
            }
            .estado-abierta {
              background-color: rgba(59, 130, 246, 0.15);
              color: rgb(37, 99, 235);
            }
            .estado-cerrada {
              background-color: rgba(107, 114, 128, 0.15);
              color: rgb(75, 85, 99);
            }
            /* Estilos para modo oscuro */
            .dark-theme .estado-abierta {
              background-color: rgba(59, 130, 246, 0.2);
              color: rgb(96, 165, 250);
            }
            .dark-theme .estado-cerrada {
              background-color: rgba(156, 163, 175, 0.2);
              color: rgb(209, 213, 219);
            }
          </style>

          <app-card-v1
            [data]="faq"
            [searchText]="pagination.value.search ?? ''"
            [showOption]="validateOptionsByUserId()"
            (optionClick)="getOptCardV1($event)"
          ></app-card-v1>
        </div>

        <!-- Mensaje cuando no hay resultados con diseño mejorado -->
        <div
          class="flex flex-col items-center justify-center py-16 px-4"
          *ngIf="!paginationResult.data.length"
        >
          <div class="bg-slate-100 dark:bg-slate-700 rounded-full p-4 mb-4">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-10 w-10 text-slate-400 dark:text-slate-300"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
          </div>
          <h3
            class="text-lg font-medium text-slate-700 dark:text-slate-200 mb-1"
          >
            No se encontraron registros
          </h3>
          <p
            class="text-sm text-slate-500 dark:text-slate-400 text-center max-w-md"
          >
            No hay preguntas frecuentes disponibles. Puede agregar una nueva
            pregunta usando el botón "Agregar nuevo".
          </p>
        </div>
      </div>

      <!-- Paginación con diseño moderno -->
      <div
        class="flex flex-col sm:flex-row justify-between items-center p-4 bg-slate-50 dark:bg-slate-800/80 border-t border-slate-200 dark:border-slate-700"
        *ngIf="paginationResult.total"
      >
        <div class="text-sm text-slate-500 dark:text-slate-400 mb-3 sm:mb-0">
          Mostrando
          <span class="font-medium text-slate-700 dark:text-slate-300">{{
            paginationResult.from
          }}</span>
          a
          <span class="font-medium text-slate-700 dark:text-slate-300">{{
            paginationResult.to
          }}</span>
          de
          <span class="font-medium text-slate-700 dark:text-slate-300">{{
            paginationResult.total
          }}</span>
          registros
        </div>

        <div class="inline-flex rounded-md shadow-sm">
          <button
            class="relative inline-flex items-center px-3 py-2 rounded-l-md border border-slate-300 bg-white text-sm font-medium text-slate-700 hover:bg-slate-50 focus:z-10 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50 disabled:cursor-not-allowed disabled:bg-slate-100 dark:bg-slate-700 dark:border-slate-600 dark:text-slate-200 dark:hover:bg-slate-600 dark:disabled:bg-slate-800 dark:disabled:text-slate-500"
            [disabled]="(pagination.value.page || 1) === 1"
            (click)="previousPage()"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-5 w-5"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fill-rule="evenodd"
                d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z"
                clip-rule="evenodd"
              />
            </svg>
            <span class="sr-only sm:not-sr-only sm:ml-1">Anterior</span>
          </button>

          <span
            class="relative -ml-px inline-flex items-center px-4 py-2 border border-slate-300 bg-blue-600 text-sm font-medium text-white dark:bg-blue-700 dark:border-blue-800"
          >
            {{ pagination.value.page || 1 }}
          </span>

          <button
            class="relative -ml-px inline-flex items-center px-3 py-2 rounded-r-md border border-slate-300 bg-white text-sm font-medium text-slate-700 hover:bg-slate-50 focus:z-10 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50 disabled:cursor-not-allowed disabled:bg-slate-100 dark:bg-slate-700 dark:border-slate-600 dark:text-slate-200 dark:hover:bg-slate-600 dark:disabled:bg-slate-800 dark:disabled:text-slate-500"
            [disabled]="
              (pagination.value.page || 1) * (pagination.value.perPage || 10) >=
              paginationResult.total
            "
            (click)="nextPage()"
          >
            <span class="sr-only sm:not-sr-only sm:mr-1">Siguiente</span>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-5 w-5"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fill-rule="evenodd"
                d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                clip-rule="evenodd"
              />
            </svg>
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Sección de contacto con diseño moderno -->
  <div
    class="max-w-screen-xl mx-auto px-4 py-12 dark:bg-slate-800 rounded-lg shadow-sm mt-8 mb-12"
  >
    <div class="flex flex-col md:flex-row items-center justify-between">
      <div class="w-full md:w-1/2 mb-8 md:mb-0 text-center md:text-left">
        <h2 class="text-2xl font-bold text-slate-800 dark:text-white mb-4">
          ¿Aún tienes preguntas?
        </h2>
        <p class="text-slate-600 dark:text-slate-300 mb-6">
          Una maravillosa serenidad se ha apoderado de todo mi alma, como estas
          dulces mañanas de primavera que disfruto con todo mi corazón.
        </p>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div class="flex flex-col items-center md:items-start">
            <div class="flex items-center mb-2">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-5 w-5 text-blue-600 dark:text-blue-400 mr-2"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
                />
              </svg>
              <div class="flex flex-col space-y-3">
                <div>
                  <span
                    class="font-medium text-slate-800 dark:text-white flex items-center"
                  >
                    <span
                      class="inline-block w-4 h-4 bg-green-500 rounded-full mr-2"
                    ></span>
                    +51 958 875 176
                  </span>
                  <span class="text-sm text-slate-600 dark:text-slate-400 ml-6"
                    >Dagner Chuman - Programador</span
                  >
                </div>
                <div>
                  <span
                    class="font-medium text-slate-800 dark:text-white flex items-center"
                  >
                    <span
                      class="inline-block w-4 h-4 bg-green-500 rounded-full mr-2"
                    ></span>
                    +51 923 945 382
                  </span>
                  <span class="text-sm text-slate-600 dark:text-slate-400 ml-6"
                    >José Torres - Programador</span
                  >
                </div>
                <div>
                  <span
                    class="font-medium text-slate-800 dark:text-white flex items-center"
                  >
                    <span
                      class="inline-block w-4 h-4 bg-green-500 rounded-full mr-2"
                    ></span>
                    +51 902 772 754
                  </span>
                  <span class="text-sm text-slate-600 dark:text-slate-400 ml-6"
                    >Enrique Piscoya - Programador</span
                  >
                </div>
              </div>
            </div>
            <p class="text-sm text-slate-500 dark:text-slate-400">
              Contacta con nuestros programadores para soporte técnico
              especializado.
            </p>
          </div>
          <div class="flex flex-col items-center md:items-start">
            <div class="flex items-center mb-2">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-5 w-5 text-blue-600 dark:text-blue-400 mr-2"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                />
              </svg>
              <span class="font-medium text-slate-800 dark:text-white"
                >seomidassc&#64;gmail.com</span
              >
            </div>
            <p class="text-sm text-slate-500 dark:text-slate-400">
              Envíanos un correo para resolver tu problema.
            </p>
          </div>
        </div>
      </div>
      <div class="w-full md:w-1/2 flex justify-center md:justify-end">
        <img
          src="assets/it_support.svg"
          alt="Soporte técnico"
          class="w-64 h-64 md:w-80 md:h-80"
        />
      </div>
    </div>
  </div>
</div>
