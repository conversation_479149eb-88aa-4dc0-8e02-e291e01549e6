<!-- <PERSON><PERSON> para Desktop -->
<div
  class="flex flex-col h-screen w-full bg-white shadow-sm relative overflow-hidden m-0 p-0 transition-colors duration-300 dark:bg-[#0a1628]"
>
  <!-- LOGO en la parte superior -->
  <div
    class="flex justify-center items-center h-14 border-b-0 bg-white w-full dark:bg-[#0a1628] mt-1"
  >
    <img
      *ngIf="!isDarkTheme"
      src="assets/logovector-MIDAS.svg"
      alt="Midas Solutions"
      routerLink="/home"
      routerLinkActive="active"
      class="w-28 sm:w-32 max-w-[85%] h-auto cursor-pointer transition-transform duration-300 hover:scale-105"
    />
    <img
      *ngIf="isDarkTheme"
      src="assets/Logotipo-sb.svg"
      alt="Midas Solutions"
      routerLink="/home"
      routerLinkActive="active"
      class="w-28 sm:w-32 max-w-[85%] h-auto cursor-pointer transition-transform duration-300 hover:scale-105"
    />
  </div>

  <!-- Lista de navegación principal -->
  <mat-nav-list
    class="flex-1 py-0.5 w-full m-0 relative overflow-y-auto overflow-x-hidden"
  >
    <ng-container *ngIf="isAuthorized">
      <!-- Si el usuario es ADMIN o PROGRAMADOR se muestran todas las rutas -->
      <ng-container *ngIf="isAdmin() || isPROGRAMADOR(); else roleMenu">
        <!-- DASHBOARDS Section -->
        <div class="px-3 py-2 mt-2">
          <h3
            class="text-xs font-poppins font-semibold text-blue-600 dark:text-blue-400 uppercase tracking-wider"
          >
            DASHBOARDS
          </h3>
        </div>

        <!-- Inicio -->
        <a
          mat-list-item
          routerLink="home"
          routerLinkActive="active"
          class="mx-0 my-0 rounded text-gray-700 flex items-center h-auto min-h-7 py-1 transition-colors dark:text-gray-200 hover:bg-blue-50 hover:text-blue-600 dark:hover:bg-blue-500/10 dark:hover:text-blue-400 dark:active:bg-blue-500/15 dark:active:text-blue-400"
        >
          <mat-icon
            class="text-gray-600 dark:text-gray-400 transition-colors mr-2 text-sm"
            >home</mat-icon
          >
          <span class="text-sm font-poppins font-medium">Home</span>
        </a>

        <!-- PAGES Section -->
        <div class="px-3 py-2 mt-4">
          <h3
            class="text-xs font-poppins font-semibold text-blue-600 dark:text-blue-400 uppercase tracking-wider"
          >
            PAGES
          </h3>
        </div>

        <a
          mat-list-item
          routerLink="manual"
          routerLinkActive="active"
          class="mx-0 my-0 rounded text-gray-700 flex items-center h-auto min-h-7 py-1 transition-colors dark:text-gray-200 hover:bg-blue-50 hover:text-blue-600 dark:hover:bg-blue-500/10 dark:hover:text-blue-400 dark:active:bg-blue-500/15 dark:active:text-blue-400"
        >
          <mat-icon
            class="text-gray-600 dark:text-gray-400 transition-colors mr-2 text-sm"
            >menu_book</mat-icon
          >
          <span class="text-sm font-poppins font-medium">Manuales</span>
        </a>

        <!-- Grupo de Calendario -->
        <a
          mat-list-item
          routerLink="calendar"
          routerLinkActive="active"
          class="mx-0 my-0 rounded text-gray-700 flex items-center h-auto min-h-7 py-1 transition-colors dark:text-gray-200 hover:bg-blue-50 hover:text-blue-600 dark:hover:bg-blue-500/10 dark:hover:text-blue-400 dark:active:bg-blue-500/15 dark:active:text-blue-400"
        >
          <mat-icon
            class="text-gray-600 dark:text-gray-400 transition-colors mr-2 text-sm"
            >calendar_today</mat-icon
          >
          <span class="text-sm font-poppins font-medium">Calendario</span>
        </a>

        <!-- APPS Section -->
        <div class="px-3 py-2 mt-4">
          <h3
            class="text-xs font-poppins font-semibold text-blue-600 dark:text-blue-400 uppercase tracking-wider"
          >
            APPS
          </h3>
        </div>

        <!-- Grupo de Coordinadores -->
        <a
          mat-list-item
          routerLink="coordinador/listar"
          routerLinkActive="active"
          class="mx-0 my-0 rounded text-gray-700 flex items-center h-auto min-h-7 py-1 transition-colors dark:text-gray-200 hover:bg-blue-50 hover:text-blue-600 dark:hover:bg-blue-500/10 dark:hover:text-blue-400 dark:active:bg-blue-500/15 dark:active:text-blue-400"
        >
          <mat-icon
            class="text-gray-600 dark:text-gray-400 transition-colors mr-2 text-sm"
            >person_add</mat-icon
          >
          <span class="text-sm font-poppins font-medium">Supervisión</span>
        </a>

        <!-- Enlace directo a Tipificaciones para ADMIN -->
        <a
          mat-list-item
          routerLink="clienteresidencial/listar"
          routerLinkActive="active"
          class="mx-0 my-0 rounded text-gray-700 flex items-center h-auto min-h-7 py-1 transition-colors dark:text-gray-200 hover:bg-blue-50 hover:text-blue-600 dark:hover:bg-blue-500/10 dark:hover:text-blue-400 dark:active:bg-blue-500/15 dark:active:text-blue-400"
        >
          <mat-icon
            class="text-gray-600 dark:text-gray-400 transition-colors mr-2 text-sm"
            >assignment</mat-icon
          >
          <span class="text-sm font-poppins font-medium">Leads</span>
        </a>

        <!-- Anuncios -->
        <a
          mat-list-item
          routerLink="anuncios/list"
          routerLinkActive="active"
          class="mx-0 my-0 rounded text-gray-700 flex items-center h-auto min-h-7 py-1 transition-colors dark:text-gray-200 hover:bg-blue-50 hover:text-blue-600 dark:hover:bg-blue-500/10 dark:hover:text-blue-400 dark:active:bg-blue-500/15 dark:active:text-blue-400"
        >
          <mat-icon
            class="text-gray-600 dark:text-gray-400 transition-colors mr-2 text-sm"
            >campaign</mat-icon
          >
          <span class="text-sm font-poppins font-medium">Anuncios</span>
        </a>

        <!-- Enlace directo a Ventas -->
        <a
          mat-list-item
          routerLink="/ventas"
          routerLinkActive="active"
          class="mx-0 my-0 rounded text-gray-700 flex items-center h-auto min-h-7 py-1 transition-colors dark:text-gray-200 hover:bg-blue-50 hover:text-blue-600 dark:hover:bg-blue-500/10 dark:hover:text-blue-400 dark:active:bg-blue-500/15 dark:active:text-blue-400"
        >
          <mat-icon
            class="text-gray-600 dark:text-gray-400 transition-colors mr-2 text-sm"
            >shopping_cart</mat-icon
          >
          <span class="text-sm font-poppins font-medium">Ventas</span>
        </a>

        <!-- Preguntas y Respuestas-->
        <a
          mat-list-item
          routerLink="faq"
          routerLinkActive="active"
          class="mx-0 my-0 rounded text-gray-700 flex items-center h-auto min-h-7 py-1 transition-colors dark:text-gray-200 hover:bg-blue-50 hover:text-blue-600 dark:hover:bg-blue-500/10 dark:hover:text-blue-400 dark:active:bg-blue-500/15 dark:active:text-blue-400"
        >
          <mat-icon
            class="text-gray-600 dark:text-gray-400 transition-colors mr-2 text-sm"
            >question_answer</mat-icon
          >
          <span class="text-sm font-poppins font-medium">Foro</span>
        </a>

        <!-- ADMINISTRATIVE Section -->
        <div class="px-3 py-2 mt-4">
          <h3
            class="text-xs font-poppins font-semibold text-blue-600 dark:text-blue-400 uppercase tracking-wider"
          >
            ADMINISTRATIVE
          </h3>
        </div>

        <!-- Usuarios -->
        <a
          mat-list-item
          routerLink="auth/listar"
          routerLinkActive="active"
          class="mx-0 my-0 rounded text-gray-700 flex items-center h-auto min-h-7 py-1 transition-colors dark:text-gray-200 hover:bg-blue-50 hover:text-blue-600 dark:hover:bg-blue-500/10 dark:hover:text-blue-400 dark:active:bg-blue-500/15 dark:active:text-blue-400"
        >
          <mat-icon
            class="text-gray-600 dark:text-gray-400 transition-colors mr-2 text-sm"
            >group</mat-icon
          >
          <span class="text-sm font-poppins font-medium">Usuarios</span>
        </a>

        <!-- IPs Permitidas -->
        <a
          mat-list-item
          routerLink="ipsPermitidas"
          routerLinkActive="active"
          class="mx-0 my-0 rounded text-gray-700 flex items-center h-auto min-h-7 py-1 transition-colors dark:text-gray-200 hover:bg-blue-50 hover:text-blue-600 dark:hover:bg-blue-500/10 dark:hover:text-blue-400 dark:active:bg-blue-500/15 dark:active:text-blue-400"
        >
          <mat-icon
            class="text-gray-600 dark:text-gray-400 transition-colors mr-2 text-sm"
            >security</mat-icon
          >
          <span class="text-sm font-poppins font-medium">IPs Permitidas</span>
        </a>

        <!-- Notificaciones -->
        <a
          mat-list-item
          routerLink="notificaciones"
          routerLinkActive="active"
          class="mx-0 my-0 rounded text-gray-700 flex items-center h-auto min-h-7 py-1 transition-colors dark:text-gray-200 hover:bg-blue-50 hover:text-blue-600 dark:hover:bg-blue-500/10 dark:hover:text-blue-400 dark:active:bg-blue-500/15 dark:active:text-blue-400"
        >
          <mat-icon
            class="text-gray-600 dark:text-gray-400 transition-colors mr-2 text-sm"
            >notifications_active</mat-icon
          >
          <span class="text-sm font-poppins font-medium">Notificaciones</span>
        </a>

        <!-- Sede -->
        <a
          mat-list-item
          routerLink="sede"
          routerLinkActive="active"
          class="mx-0 my-0 rounded text-gray-700 flex items-center h-auto min-h-7 py-1 transition-colors dark:text-gray-200 hover:bg-blue-50 hover:text-blue-600 dark:hover:bg-blue-500/10 dark:hover:text-blue-400 dark:active:bg-blue-500/15 dark:active:text-blue-400"
        >
          <mat-icon
            class="text-gray-600 dark:text-gray-400 transition-colors mr-2 text-sm"
            >location_on</mat-icon
          >
          <span class="text-sm font-poppins font-medium">Sede</span>
        </a>

        <!-- Datos Landing (plegable) -->
        <a
          mat-list-item
          (click)="isLandingOpen = !isLandingOpen"
          class="mx-0 my-0 rounded text-gray-700 flex items-center h-auto min-h-7 py-1 transition-colors dark:text-gray-200 hover:bg-blue-50 hover:text-blue-600 dark:hover:bg-blue-500/10 dark:hover:text-blue-400 cursor-pointer"
        >
          <mat-icon
            class="text-gray-600 dark:text-gray-400 transition-colors mr-2 text-sm"
            >folder_open</mat-icon
          >
          <span class="text-sm font-poppins font-medium">Datos Landing</span>
          <mat-icon class="ml-auto text-gray-500 dark:text-gray-400 text-xs">{{
            isLandingOpen ? "expand_less" : "expand_more"
          }}</mat-icon>
        </a>

        <!-- Submenús desplegables -->
        <div *ngIf="isLandingOpen" class="pl-5">
          <a
            mat-list-item
            routerLink="landing-contact"
            routerLinkActive="active"
            class="mx-0 my-0 rounded text-gray-700 flex items-center h-auto min-h-6 py-0.5 transition-colors dark:text-gray-200 hover:bg-blue-50 hover:text-blue-600 dark:hover:bg-blue-500/10 dark:hover:text-blue-400 dark:active:bg-blue-500/15 dark:active:text-blue-400"
          >
            <mat-icon
              class="text-gray-600 dark:text-gray-400 transition-colors mr-2 text-sm"
              >contact_mail</mat-icon
            >
            <span class="text-sm font-poppins font-medium">Contacto</span>
          </a>

          <a
            mat-list-item
            routerLink="postulacion"
            routerLinkActive="active"
            class="mx-0 my-0 rounded text-gray-700 flex items-center h-auto min-h-6 py-0.5 transition-colors dark:text-gray-200 hover:bg-blue-50 hover:text-blue-600 dark:hover:bg-blue-500/10 dark:hover:text-blue-400 dark:active:bg-blue-500/15 dark:active:text-blue-400"
          >
            <mat-icon
              class="text-gray-600 dark:text-gray-400 transition-colors mr-2 text-sm"
              >how_to_reg</mat-icon
            >
            <span class="text-sm font-poppins font-medium">Postulación</span>
          </a>
        </div>

        <!-- EDUCATION Section -->
        <div class="px-3 py-2 mt-4">
          <h3
            class="text-xs font-poppins font-semibold text-blue-600 dark:text-blue-400 uppercase tracking-wider"
          >
            EDUCATION
          </h3>
        </div>

        <!-- Cursos (Admin) -->
        <a
          mat-list-item
          routerLink="cursos"
          routerLinkActive="active"
          class="mx-0 my-0 rounded text-gray-700 flex items-center h-auto min-h-7 py-1 transition-colors dark:text-gray-200 hover:bg-blue-50 hover:text-blue-600 dark:hover:bg-blue-500/10 dark:hover:text-blue-400 dark:active:bg-blue-500/15 dark:active:text-blue-400"
        >
          <mat-icon
            class="text-gray-600 dark:text-gray-400 transition-colors mr-2 text-sm"
            >school</mat-icon
          >
          <span class="text-sm font-poppins font-medium"
            >Administrar Cursos</span
          >
        </a>

        <!-- Mis Cursos -->
        <a
          mat-list-item
          routerLink="cursos/mis-cursos"
          routerLinkActive="active"
          class="mx-0 my-0 rounded text-gray-700 flex items-center h-auto min-h-7 py-1 transition-colors dark:text-gray-200 hover:bg-blue-50 hover:text-blue-600 dark:hover:bg-blue-500/10 dark:hover:text-blue-400 dark:active:bg-blue-500/15 dark:active:text-blue-400"
        >
          <mat-icon
            class="text-gray-600 dark:text-gray-400 transition-colors mr-2 text-sm"
            >school</mat-icon
          >
          <span class="text-sm font-poppins font-medium">Mis Cursos</span>
        </a>

        <!-- TOOLS Section -->
        <div class="px-3 py-2 mt-4">
          <h3 class="text-xs font-poppins font-semibold text-blue-600 dark:text-blue-400 uppercase tracking-wider">TOOLS</h3>
        </div>

        <!-- Ejemplo de Mapa -->
        <a
          mat-list-item
          routerLink="/map-example"
          routerLinkActive="active"
          class="mx-0 my-0 rounded text-gray-700 flex items-center h-auto min-h-7 py-1 transition-colors dark:text-gray-200 hover:bg-blue-50 hover:text-blue-600 dark:hover:bg-blue-500/10 dark:hover:text-blue-400 dark:active:bg-blue-500/15 dark:active:text-blue-400"
        >
          <mat-icon
            class="text-gray-600 dark:text-gray-400 transition-colors mr-2 text-sm"
            >map</mat-icon
          >
          <span class="text-sm font-poppins font-medium">Mapa</span>
        </a>

        <!-- Enlace a Encuestas -->
        <a
          mat-list-item
          routerLink="/encuestas"
          routerLinkActive="active"
          class="mx-0 my-0 rounded text-gray-700 flex items-center h-auto min-h-7 py-1 transition-colors dark:text-gray-200 hover:bg-blue-50 hover:text-blue-600 dark:hover:bg-blue-500/10 dark:hover:text-blue-400 dark:active:bg-blue-500/15 dark:active:text-blue-400"
        >
          <mat-icon
            class="text-gray-600 dark:text-gray-400 transition-colors mr-2 text-sm"
            >quiz</mat-icon
          >
          <span class="text-sm font-poppins font-medium">Encuestas</span>
        </a>
      </ng-container>

      <!-- Menú para usuarios que NO son ADMIN -->
      <ng-template #roleMenu>
        <!-- Auditor Menu -->
        <ng-container *ngIf="isAuditor()">
          <!-- Inicio -->
          <a
            mat-list-item
            routerLink="home"
            routerLinkActive="active"
            class="mx-0 my-0 rounded text-gray-700 flex items-center h-auto min-h-7 py-1 transition-colors dark:text-gray-200 hover:bg-blue-50 hover:text-blue-600 dark:hover:bg-blue-500/10 dark:hover:text-blue-400 dark:active:bg-blue-500/15 dark:active:text-blue-400"
          >
            <mat-icon
              class="text-gray-600 dark:text-gray-400 transition-colors mr-2 text-sm"
              >home</mat-icon
            >
            <span class="text-xs font-medium">Home</span>
          </a>

          <!-- Manuales -->
          <a
            mat-list-item
            routerLink="manual"
            routerLinkActive="active"
            class="mx-0 my-0 rounded text-gray-700 flex items-center h-auto min-h-7 py-1 transition-colors dark:text-gray-200 hover:bg-blue-50 hover:text-blue-600 dark:hover:bg-blue-500/10 dark:hover:text-blue-400 dark:active:bg-blue-500/15 dark:active:text-blue-400"
          >
            <mat-icon
              class="text-gray-600 dark:text-gray-400 transition-colors mr-2 text-sm"
              >menu_book</mat-icon
            >
            <span class="text-xs font-medium">Manuales</span>
          </a>

          <!-- Calendario -->
          <a
            mat-list-item
            routerLink="calendar"
            routerLinkActive="active"
            class="mx-0 my-0 rounded text-gray-700 flex items-center h-auto min-h-7 py-1 transition-colors dark:text-gray-200 hover:bg-blue-50 hover:text-blue-600 dark:hover:bg-blue-500/10 dark:hover:text-blue-400 dark:active:bg-blue-500/15 dark:active:text-blue-400"
          >
            <mat-icon
              class="text-gray-600 dark:text-gray-400 transition-colors mr-2 text-sm"
              >calendar_today</mat-icon
            >
            <span class="text-xs font-medium">Calendario</span>
          </a>

          <!-- Mis Cursos -->
          <a
            mat-list-item
            routerLink="cursos/mis-cursos"
            routerLinkActive="active"
            class="mx-0 my-0 rounded text-gray-700 flex items-center h-auto min-h-7 py-1 transition-colors dark:text-gray-200 hover:bg-blue-50 hover:text-blue-600 dark:hover:bg-blue-500/10 dark:hover:text-blue-400 dark:active:bg-blue-500/15 dark:active:text-blue-400"
          >
            <mat-icon
              class="text-gray-600 dark:text-gray-400 transition-colors mr-2 text-sm"
              >school</mat-icon
            >
            <span class="text-xs font-medium">Mis Cursos</span>
          </a>

          <!-- Solo listar clientes -->
          <a
            mat-list-item
            routerLink="clienteresidencial/listar"
            routerLinkActive="active"
            class="mx-0 my-0 rounded text-gray-700 flex items-center h-auto min-h-7 py-1 transition-colors dark:text-gray-200 hover:bg-blue-50 hover:text-blue-600 dark:hover:bg-blue-500/10 dark:hover:text-blue-400 dark:active:bg-blue-500/15 dark:active:text-blue-400"
          >
            <mat-icon
              class="text-gray-600 dark:text-gray-400 transition-colors mr-2 text-sm"
              >how_to_reg</mat-icon
            >
            <span class="text-xs font-medium">Listar Clientes</span>
          </a>

          <!-- Preguntas y Respuestas-->
          <a
            mat-list-item
            routerLink="faq"
            routerLinkActive="active"
            class="mx-0 my-0 rounded text-gray-700 flex items-center h-auto min-h-7 py-1 transition-colors dark:text-gray-200 hover:bg-blue-50 hover:text-blue-600 dark:hover:bg-blue-500/10 dark:hover:text-blue-400 dark:active:bg-blue-500/15 dark:active:text-blue-400"
          >
            <mat-icon
              class="text-gray-600 dark:text-gray-400 transition-colors mr-2 text-sm"
              >question_answer</mat-icon
            >
            <span class="text-xs font-medium">Foro</span>
          </a>

          <!-- Encuestas -->
          <a
            mat-list-item
            routerLink="/encuestas"
            routerLinkActive="active"
            class="mx-0 my-0 rounded text-gray-700 flex items-center h-auto min-h-7 py-1 transition-colors dark:text-gray-200 hover:bg-blue-50 hover:text-blue-600 dark:hover:bg-blue-500/10 dark:hover:text-blue-400 dark:active:bg-blue-500/15 dark:active:text-blue-400"
          >
            <mat-icon
              class="text-gray-600 dark:text-gray-400 transition-colors mr-2 text-sm"
              >quiz</mat-icon
            >
            <span class="text-xs font-medium">Encuestas</span>
          </a>
        </ng-container>

        <!-- BackOffice Menu -->
        <ng-container *ngIf="isBackOffice()">
          <!-- Inicio -->
          <a
            mat-list-item
            routerLink="home"
            routerLinkActive="active"
            class="mx-0 my-0 rounded text-gray-700 flex items-center h-auto min-h-7 py-1 transition-colors dark:text-gray-200 hover:bg-blue-50 hover:text-blue-600 dark:hover:bg-blue-500/10 dark:hover:text-blue-400 dark:active:bg-blue-500/15 dark:active:text-blue-400"
          >
            <mat-icon
              class="text-gray-600 dark:text-gray-400 transition-colors mr-2 text-sm"
              >home</mat-icon
            >
            <span class="text-xs font-medium">Home</span>
          </a>

          <!-- Manuales -->
          <a
            mat-list-item
            routerLink="manual"
            routerLinkActive="active"
            class="mx-0 my-0 rounded text-gray-700 flex items-center h-auto min-h-7 py-1 transition-colors dark:text-gray-200 hover:bg-blue-50 hover:text-blue-600 dark:hover:bg-blue-500/10 dark:hover:text-blue-400 dark:active:bg-blue-500/15 dark:active:text-blue-400"
          >
            <mat-icon
              class="text-gray-600 dark:text-gray-400 transition-colors mr-2 text-sm"
              >menu_book</mat-icon
            >
            <span class="text-xs font-medium">Manuales</span>
          </a>

          <!-- Calendario -->
          <a
            mat-list-item
            routerLink="calendar"
            routerLinkActive="active"
            class="mx-0 my-0 rounded text-gray-700 flex items-center h-auto min-h-7 py-1 transition-colors dark:text-gray-200 hover:bg-blue-50 hover:text-blue-600 dark:hover:bg-blue-500/10 dark:hover:text-blue-400 dark:active:bg-blue-500/15 dark:active:text-blue-400"
          >
            <mat-icon
              class="text-gray-600 dark:text-gray-400 transition-colors mr-2 text-sm"
              >calendar_today</mat-icon
            >
            <span class="text-xs font-medium">Calendario</span>
          </a>

          <!-- Listar Clientes general -->
          <a
            mat-list-item
            routerLink="clienteresidencial/listar"
            routerLinkActive="active"
            class="mx-0 my-0 rounded text-gray-700 flex items-center h-auto min-h-7 py-1 transition-colors dark:text-gray-200 hover:bg-blue-50 hover:text-blue-600 dark:hover:bg-blue-500/10 dark:hover:text-blue-400 dark:active:bg-blue-500/15 dark:active:text-blue-400"
          >
            <mat-icon
              class="text-gray-600 dark:text-gray-400 transition-colors mr-2 text-sm"
              >group</mat-icon
            >
            <span class="text-xs font-medium">Listar Clientes general</span>
          </a>

          <!-- Enlace directo a Ventas -->
          <a
            mat-list-item
            routerLink="/ventas"
            routerLinkActive="active"
            class="mx-0 my-0 rounded text-gray-700 flex items-center h-auto min-h-7 py-1 transition-colors dark:text-gray-200 hover:bg-blue-50 hover:text-blue-600 dark:hover:bg-blue-500/10 dark:hover:text-blue-400 dark:active:bg-blue-500/15 dark:active:text-blue-400"
          >
            <mat-icon
              class="text-gray-600 dark:text-gray-400 transition-colors mr-2 text-sm"
              >shopping_cart</mat-icon
            >
            <span class="text-xs font-medium">Ventas</span>
          </a>

          <!-- Preguntas y Respuestas-->
          <a
            mat-list-item
            routerLink="faq"
            routerLinkActive="active"
            class="mx-0 my-0 rounded text-gray-700 flex items-center h-auto min-h-7 py-1 transition-colors dark:text-gray-200 hover:bg-blue-50 hover:text-blue-600 dark:hover:bg-blue-500/10 dark:hover:text-blue-400 dark:active:bg-blue-500/15 dark:active:text-blue-400"
          >
            <mat-icon
              class="text-gray-600 dark:text-gray-400 transition-colors mr-2 text-sm"
              >question_answer</mat-icon
            >
            <span class="text-xs font-medium">Foro</span>
          </a>

          <!-- Mis Cursos -->
          <a
            mat-list-item
            routerLink="cursos/mis-cursos"
            routerLinkActive="active"
            class="mx-0 my-0 rounded text-gray-700 flex items-center h-auto min-h-7 py-1 transition-colors dark:text-gray-200 hover:bg-blue-50 hover:text-blue-600 dark:hover:bg-blue-500/10 dark:hover:text-blue-400 dark:active:bg-blue-500/15 dark:active:text-blue-400"
          >
            <mat-icon
              class="text-gray-600 dark:text-gray-400 transition-colors mr-2 text-sm"
              >school</mat-icon
            >
            <span class="text-xs font-medium">Mis Cursos</span>
          </a>

          <!-- Encuestas -->
          <a
            mat-list-item
            routerLink="/encuestas"
            routerLinkActive="active"
            class="mx-0 my-0 rounded text-gray-700 flex items-center h-auto min-h-7 py-1 transition-colors dark:text-gray-200 hover:bg-blue-50 hover:text-blue-600 dark:hover:bg-blue-500/10 dark:hover:text-blue-400 dark:active:bg-blue-500/15 dark:active:text-blue-400"
          >
            <mat-icon
              class="text-gray-600 dark:text-gray-400 transition-colors mr-2 text-sm"
              >quiz</mat-icon
            >
            <span class="text-xs font-medium">Encuestas</span>
          </a>
        </ng-container>
        <!-- BackOffice Tramitador Menu -->
        <ng-container *ngIf="isBackOfficeTramitador()">
          <!-- Inicio -->
          <a
            mat-list-item
            routerLink="home"
            routerLinkActive="active"
            class="mx-1 my-0.5 rounded text-gray-700 flex items-center h-auto min-h-8 py-1.5 transition-colors dark:text-gray-200 hover:bg-blue-50 hover:text-blue-600 dark:hover:bg-blue-500/10 dark:hover:text-blue-400 dark:active:bg-blue-500/15 dark:active:text-blue-400"
          >
            <mat-icon
              class="text-gray-600 dark:text-gray-400 transition-colors mr-1.5 text-base"
              >home</mat-icon
            >
            <span class="pl-0.5 text-xs font-medium">Home</span>
          </a>

          <!-- Manuales -->
          <a
            mat-list-item
            routerLink="manual"
            routerLinkActive="active"
            class="mx-1 my-0.5 rounded text-gray-700 flex items-center h-auto min-h-8 py-1.5 transition-colors dark:text-gray-200 hover:bg-blue-50 hover:text-blue-600 dark:hover:bg-blue-500/10 dark:hover:text-blue-400 dark:active:bg-blue-500/15 dark:active:text-blue-400"
          >
            <mat-icon
              class="text-gray-600 dark:text-gray-400 transition-colors mr-1.5 text-base"
              >menu_book</mat-icon
            >
            <span class="pl-0.5 text-xs font-medium">Manuales</span>
          </a>

          <!-- Calendario -->
          <a
            mat-list-item
            routerLink="calendar"
            routerLinkActive="active"
            class="mx-1 my-0.5 rounded text-gray-700 flex items-center h-auto min-h-8 py-1.5 transition-colors dark:text-gray-200 hover:bg-blue-50 hover:text-blue-600 dark:hover:bg-blue-500/10 dark:hover:text-blue-400 dark:active:bg-blue-500/15 dark:active:text-blue-400"
          >
            <mat-icon
              class="text-gray-600 dark:text-gray-400 transition-colors mr-1.5 text-base"
              >calendar_today</mat-icon
            >
            <span class="pl-0.5 text-xs font-medium">Calendario</span>
          </a>

          <!-- Listar Clientes general -->
          <a
            mat-list-item
            routerLink="clienteresidencial/listar"
            routerLinkActive="active"
            class="mx-1 my-0.5 rounded text-gray-700 flex items-center h-auto min-h-8 py-1.5 transition-colors dark:text-gray-200 hover:bg-blue-50 hover:text-blue-600 dark:hover:bg-blue-500/10 dark:hover:text-blue-400 dark:active:bg-blue-500/15 dark:active:text-blue-400"
          >
            <mat-icon
              class="text-gray-600 dark:text-gray-400 transition-colors mr-1.5 text-base"
              >group</mat-icon
            >
            <span class="pl-0.5 text-xs font-medium"
              >Listar Clientes general</span
            >
          </a>

          <!-- Enlace directo a Ventas -->
          <a
            mat-list-item
            routerLink="/ventas"
            routerLinkActive="active"
            class="mx-1 my-0.5 rounded text-gray-700 flex items-center h-auto min-h-8 py-1.5 transition-colors dark:text-gray-200 hover:bg-blue-50 hover:text-blue-600 dark:hover:bg-blue-500/10 dark:hover:text-blue-400 dark:active:bg-blue-500/15 dark:active:text-blue-400"
          >
            <mat-icon
              class="text-gray-600 dark:text-gray-400 transition-colors mr-1.5 text-base"
              >shopping_cart</mat-icon
            >
            <span class="pl-0.5 text-xs font-medium">Ventas</span>
          </a>

          <!-- Preguntas y Respuestas-->
          <a
            mat-list-item
            routerLink="faq"
            routerLinkActive="active"
            class="mx-1 my-0.5 rounded text-gray-700 flex items-center h-auto min-h-8 py-1.5 transition-colors dark:text-gray-200 hover:bg-blue-50 hover:text-blue-600 dark:hover:bg-blue-500/10 dark:hover:text-blue-400 dark:active:bg-blue-500/15 dark:active:text-blue-400"
          >
            <mat-icon
              class="text-gray-600 dark:text-gray-400 transition-colors mr-1.5 text-base"
              >question_answer</mat-icon
            >
            <span class="pl-0.5 text-xs font-medium">Foro</span>
          </a>

          <!-- Mis Cursos -->
          <a
            mat-list-item
            routerLink="cursos/mis-cursos"
            routerLinkActive="active"
            class="mx-1 my-0.5 rounded text-gray-700 flex items-center h-auto min-h-8 py-1.5 transition-colors dark:text-gray-200 hover:bg-blue-50 hover:text-blue-600 dark:hover:bg-blue-500/10 dark:hover:text-blue-400 dark:active:bg-blue-500/15 dark:active:text-blue-400"
          >
            <mat-icon
              class="text-gray-600 dark:text-gray-400 transition-colors mr-1.5 text-base"
              >school</mat-icon
            >
            <span class="pl-0.5 text-xs font-medium">Mis Cursos</span>
          </a>

          <!-- Encuestas -->
          <a
            mat-list-item
            routerLink="/encuestas"
            routerLinkActive="active"
            class="mx-1 my-0.5 rounded text-gray-700 flex items-center h-auto min-h-8 py-1.5 transition-colors dark:text-gray-200 hover:bg-blue-50 hover:text-blue-600 dark:hover:bg-blue-500/10 dark:hover:text-blue-400 dark:active:bg-blue-500/15 dark:active:text-blue-400"
          >
            <mat-icon
              class="text-gray-600 dark:text-gray-400 transition-colors mr-1.5 text-base"
              >quiz</mat-icon
            >
            <span class="pl-0.5 text-xs font-medium">Encuestas</span>
          </a>
        </ng-container>
        <!-- Coordinador Menu -->
        <ng-container *ngIf="isCoordinador()">
          <!-- Inicio -->
          <a
            mat-list-item
            routerLink="home"
            routerLinkActive="active"
            class="mx-1 my-0.5 rounded text-gray-700 flex items-center h-auto min-h-8 py-1.5 transition-colors dark:text-gray-200 hover:bg-blue-50 hover:text-blue-600 dark:hover:bg-blue-500/10 dark:hover:text-blue-400 dark:active:bg-blue-500/15 dark:active:text-blue-400"
          >
            <mat-icon
              class="text-gray-600 dark:text-gray-400 transition-colors mr-1.5 text-base"
              >home</mat-icon
            >
            <span class="pl-0.5 text-xs font-medium">Home</span>
          </a>

          <!-- Manuales -->
          <a
            mat-list-item
            routerLink="manual"
            routerLinkActive="active"
            class="mx-1 my-0.5 rounded text-gray-700 flex items-center h-auto min-h-8 py-1.5 transition-colors dark:text-gray-200 hover:bg-blue-50 hover:text-blue-600 dark:hover:bg-blue-500/10 dark:hover:text-blue-400 dark:active:bg-blue-500/15 dark:active:text-blue-400"
          >
            <mat-icon
              class="text-gray-600 dark:text-gray-400 transition-colors mr-1.5 text-base"
              >menu_book</mat-icon
            >
            <span class="pl-0.5 text-xs font-medium">Manuales</span>
          </a>

          <!-- Mis Cursos -->
          <a
            mat-list-item
            routerLink="cursos/mis-cursos"
            routerLinkActive="active"
            class="mx-1 my-0.5 rounded text-gray-700 flex items-center h-auto min-h-8 py-1.5 transition-colors dark:text-gray-200 hover:bg-blue-50 hover:text-blue-600 dark:hover:bg-blue-500/10 dark:hover:text-blue-400 dark:active:bg-blue-500/15 dark:active:text-blue-400"
          >
            <mat-icon
              class="text-gray-600 dark:text-gray-400 transition-colors mr-1.5 text-base"
              >school</mat-icon
            >
            <span class="pl-0.5 text-xs font-medium">Mis Cursos</span>
          </a>

          <!-- Calendario -->
          <a
            mat-list-item
            routerLink="calendar"
            routerLinkActive="active"
            class="mx-1 my-0.5 rounded text-gray-700 flex items-center h-auto min-h-8 py-1.5 transition-colors dark:text-gray-200 hover:bg-blue-50 hover:text-blue-600 dark:hover:bg-blue-500/10 dark:hover:text-blue-400 dark:active:bg-blue-500/15 dark:active:text-blue-400"
          >
            <mat-icon
              class="text-gray-600 dark:text-gray-400 transition-colors mr-1.5 text-base"
              >calendar_today</mat-icon
            >
            <span class="pl-0.5 text-xs font-medium">Calendario</span>
          </a>

          <!-- Enlace directo a Tipificaciones para COORDINADOR -->
          <a
            mat-list-item
            routerLink="coordinador/obtenerclientesdeasesor"
            routerLinkActive="active"
            class="mx-1 my-0.5 rounded text-gray-700 flex items-center h-auto min-h-8 py-1.5 transition-colors dark:text-gray-200 hover:bg-blue-50 hover:text-blue-600 dark:hover:bg-blue-500/10 dark:hover:text-blue-400 dark:active:bg-blue-500/15 dark:active:text-blue-400"
          >
            <mat-icon
              class="text-gray-600 dark:text-gray-400 transition-colors mr-1.5 text-base"
              >assignment</mat-icon
            >
            <span class="pl-0.5 text-xs font-medium">Leads</span>
          </a>

          <!-- Enlace directo a Ventas -->
          <a
            mat-list-item
            routerLink="/ventas"
            routerLinkActive="active"
            class="mx-1 my-0.5 rounded text-gray-700 flex items-center h-auto min-h-8 py-1.5 transition-colors dark:text-gray-200 hover:bg-blue-50 hover:text-blue-600 dark:hover:bg-blue-500/10 dark:hover:text-blue-400 dark:active:bg-blue-500/15 dark:active:text-blue-400"
          >
            <mat-icon
              class="text-gray-600 dark:text-gray-400 transition-colors mr-1.5 text-base"
              >shopping_cart</mat-icon
            >
            <span class="pl-0.5 text-xs font-medium">Ventas</span>
          </a>

          <!-- Preguntas y Respuestas-->
          <a
            mat-list-item
            routerLink="faq"
            routerLinkActive="active"
            class="mx-1 my-0.5 rounded text-gray-700 flex items-center h-auto min-h-8 py-1.5 transition-colors dark:text-gray-200 hover:bg-blue-50 hover:text-blue-600 dark:hover:bg-blue-500/10 dark:hover:text-blue-400 dark:active:bg-blue-500/15 dark:active:text-blue-400"
          >
            <mat-icon
              class="text-gray-600 dark:text-gray-400 transition-colors mr-1.5 text-base"
              >question_answer</mat-icon
            >
            <span class="pl-0.5 text-xs font-medium">Foro</span>
          </a>

          <!-- Encuestas -->
          <a
            mat-list-item
            routerLink="/encuestas"
            routerLinkActive="active"
            class="mx-1 my-0.5 rounded text-gray-700 flex items-center h-auto min-h-8 py-1.5 transition-colors dark:text-gray-200 hover:bg-blue-50 hover:text-blue-600 dark:hover:bg-blue-500/10 dark:hover:text-blue-400 dark:active:bg-blue-500/15 dark:active:text-blue-400"
          >
            <mat-icon
              class="text-gray-600 dark:text-gray-400 transition-colors mr-1.5 text-base"
              >quiz</mat-icon
            >
            <span class="pl-0.5 text-xs font-medium">Encuestas</span>
          </a>
        </ng-container>

        <!-- Psicólogo Menu -->
        <ng-container *ngIf="isPsicologo()">
          <!-- Inicio -->
          <a
            mat-list-item
            routerLink="home"
            routerLinkActive="active"
            class="mx-0 my-0 rounded text-gray-700 flex items-center h-auto min-h-7 py-1 transition-colors dark:text-gray-200 hover:bg-blue-50 hover:text-blue-600 dark:hover:bg-blue-500/10 dark:hover:text-blue-400 dark:active:bg-blue-500/15 dark:active:text-blue-400"
          >
            <mat-icon
              class="text-gray-600 dark:text-gray-400 transition-colors mr-2 text-sm"
              >home</mat-icon
            >
            <span class="text-xs font-medium">Home</span>
          </a>

          <!-- Manuales -->
          <a
            mat-list-item
            routerLink="manual"
            routerLinkActive="active"
            class="mx-0 my-0 rounded text-gray-700 flex items-center h-auto min-h-7 py-1 transition-colors dark:text-gray-200 hover:bg-blue-50 hover:text-blue-600 dark:hover:bg-blue-500/10 dark:hover:text-blue-400 dark:active:bg-blue-500/15 dark:active:text-blue-400"
          >
            <mat-icon
              class="text-gray-600 dark:text-gray-400 transition-colors mr-2 text-sm"
              >menu_book</mat-icon
            >
            <span class="text-xs font-medium">Manuales</span>
          </a>

          <!-- Calendario -->
          <a
            mat-list-item
            routerLink="calendar"
            routerLinkActive="active"
            class="mx-0 my-0 rounded text-gray-700 flex items-center h-auto min-h-7 py-1 transition-colors dark:text-gray-200 hover:bg-blue-50 hover:text-blue-600 dark:hover:bg-blue-500/10 dark:hover:text-blue-400 dark:active:bg-blue-500/15 dark:active:text-blue-400"
          >
            <mat-icon
              class="text-gray-600 dark:text-gray-400 transition-colors mr-2 text-sm"
              >calendar_today</mat-icon
            >
            <span class="text-xs font-medium">Calendario</span>
          </a>

          <!-- Encuestas -->
          <a
            mat-list-item
            routerLink="/encuestas"
            routerLinkActive="active"
            class="mx-0 my-0 rounded text-gray-700 flex items-center h-auto min-h-7 py-1 transition-colors dark:text-gray-200 hover:bg-blue-50 hover:text-blue-600 dark:hover:bg-blue-500/10 dark:hover:text-blue-400 dark:active:bg-blue-500/15 dark:active:text-blue-400"
          >
            <mat-icon
              class="text-gray-600 dark:text-gray-400 transition-colors mr-2 text-sm"
              >quiz</mat-icon
            >
            <span class="text-xs font-medium">Encuestas</span>
          </a>

          <!-- Mis Cursos -->
          <a
            mat-list-item
            routerLink="cursos/mis-cursos"
            routerLinkActive="active"
            class="mx-0 my-0 rounded text-gray-700 flex items-center h-auto min-h-7 py-1 transition-colors dark:text-gray-200 hover:bg-blue-50 hover:text-blue-600 dark:hover:bg-blue-500/10 dark:hover:text-blue-400 dark:active:bg-blue-500/15 dark:active:text-blue-400"
          >
            <mat-icon
              class="text-gray-600 dark:text-gray-400 transition-colors mr-2 text-sm"
              >school</mat-icon
            >
            <span class="text-xs font-medium">Mis Cursos</span>
          </a>
        </ng-container>

        <!-- Gerencia Menu -->
        <ng-container *ngIf="isGERENCIA()">
          <!-- Inicio -->
          <a
            mat-list-item
            routerLink="home"
            routerLinkActive="active"
            class="mx-0 my-0 rounded text-gray-700 flex items-center h-auto min-h-7 py-1 transition-colors dark:text-gray-200 hover:bg-blue-50 hover:text-blue-600 dark:hover:bg-blue-500/10 dark:hover:text-blue-400 dark:active:bg-blue-500/15 dark:active:text-blue-400"
          >
            <mat-icon
              class="text-gray-600 dark:text-gray-400 transition-colors mr-2 text-sm"
              >home</mat-icon
            >
            <span class="text-xs font-medium">Home</span>
          </a>

          <!-- Manuales -->
          <a
            mat-list-item
            routerLink="manual"
            routerLinkActive="active"
            class="mx-0 my-0 rounded text-gray-700 flex items-center h-auto min-h-7 py-1 transition-colors dark:text-gray-200 hover:bg-blue-50 hover:text-blue-600 dark:hover:bg-blue-500/10 dark:hover:text-blue-400 dark:active:bg-blue-500/15 dark:active:text-blue-400"
          >
            <mat-icon
              class="text-gray-600 dark:text-gray-400 transition-colors mr-2 text-sm"
              >menu_book</mat-icon
            >
            <span class="text-xs font-medium">Manuales</span>
          </a>

          <!-- Calendario/Agenda -->
          <a
            mat-list-item
            routerLink="calendar"
            routerLinkActive="active"
            class="mx-0 my-0 rounded text-gray-700 flex items-center h-auto min-h-7 py-1 transition-colors dark:text-gray-200 hover:bg-blue-50 hover:text-blue-600 dark:hover:bg-blue-500/10 dark:hover:text-blue-400 dark:active:bg-blue-500/15 dark:active:text-blue-400"
          >
            <mat-icon
              class="text-gray-600 dark:text-gray-400 transition-colors mr-2 text-sm"
              >calendar_today</mat-icon
            >
            <span class="text-xs font-medium">Agenda</span>
          </a>

          <!-- Anuncios -->
          <a
            mat-list-item
            routerLink="anuncios/list"
            routerLinkActive="active"
            class="mx-0 my-0 rounded text-gray-700 flex items-center h-auto min-h-7 py-1 transition-colors dark:text-gray-200 hover:bg-blue-50 hover:text-blue-600 dark:hover:bg-blue-500/10 dark:hover:text-blue-400 dark:active:bg-blue-500/15 dark:active:text-blue-400"
          >
            <mat-icon
              class="text-gray-600 dark:text-gray-400 transition-colors mr-2 text-sm"
              >campaign</mat-icon
            >
            <span class="text-xs font-medium">Anuncios</span>
          </a>

          <!-- Mis Cursos -->
          <a
            mat-list-item
            routerLink="cursos/mis-cursos"
            routerLinkActive="active"
            class="mx-0 my-0 rounded text-gray-700 flex items-center h-auto min-h-7 py-1 transition-colors dark:text-gray-200 hover:bg-blue-50 hover:text-blue-600 dark:hover:bg-blue-500/10 dark:hover:text-blue-400 dark:active:bg-blue-500/15 dark:active:text-blue-400"
          >
            <mat-icon
              class="text-gray-600 dark:text-gray-400 transition-colors mr-2 text-sm"
              >school</mat-icon
            >
            <span class="text-xs font-medium">Mis Cursos</span>
          </a>

          <!-- Preguntas y Respuestas-->
          <a
            mat-list-item
            routerLink="faq"
            routerLinkActive="active"
            class="mx-0 my-0 rounded text-gray-700 flex items-center h-auto min-h-7 py-1 transition-colors dark:text-gray-200 hover:bg-blue-50 hover:text-blue-600 dark:hover:bg-blue-500/10 dark:hover:text-blue-400 dark:active:bg-blue-500/15 dark:active:text-blue-400"
          >
            <mat-icon
              class="text-gray-600 dark:text-gray-400 transition-colors mr-2 text-sm"
              >question_answer</mat-icon
            >
            <span class="text-xs font-medium">Foro</span>
          </a>

          <!-- Encuestas -->
          <a
            mat-list-item
            routerLink="/encuestas"
            routerLinkActive="active"
            class="mx-0 my-0 rounded text-gray-700 flex items-center h-auto min-h-7 py-1 transition-colors dark:text-gray-200 hover:bg-blue-50 hover:text-blue-600 dark:hover:bg-blue-500/10 dark:hover:text-blue-400 dark:active:bg-blue-500/15 dark:active:text-blue-400"
          >
            <mat-icon
              class="text-gray-600 dark:text-gray-400 transition-colors mr-2 text-sm"
              >quiz</mat-icon
            >
            <span class="text-xs font-medium">Encuestas</span>
          </a>
        </ng-container>

        <!-- Asesor Menu -->
        <ng-container *ngIf="isAsesor()">
          <!-- Inicio -->
          <a
            mat-list-item
            routerLink="home"
            routerLinkActive="active"
            class="mx-0 my-0 rounded text-gray-700 flex items-center h-auto min-h-7 py-1 transition-colors dark:text-gray-200 hover:bg-blue-50 hover:text-blue-600 dark:hover:bg-blue-500/10 dark:hover:text-blue-400 dark:active:bg-blue-500/15 dark:active:text-blue-400"
          >
            <mat-icon
              class="text-gray-600 dark:text-gray-400 transition-colors mr-2 text-sm"
              >home</mat-icon
            >
            <span class="text-xs font-medium">Home</span>
          </a>

          <!-- Manuales -->
          <a
            mat-list-item
            routerLink="manual"
            routerLinkActive="active"
            class="mx-0 my-0 rounded text-gray-700 flex items-center h-auto min-h-7 py-1 transition-colors dark:text-gray-200 hover:bg-blue-50 hover:text-blue-600 dark:hover:bg-blue-500/10 dark:hover:text-blue-400 dark:active:bg-blue-500/15 dark:active:text-blue-400"
          >
            <mat-icon
              class="text-gray-600 dark:text-gray-400 transition-colors mr-2 text-sm"
              >menu_book</mat-icon
            >
            <span class="text-xs font-medium">Manuales</span>
          </a>

          <!-- Calendario -->
          <a
            mat-list-item
            routerLink="calendar"
            routerLinkActive="active"
            class="mx-0 my-0 rounded text-gray-700 flex items-center h-auto min-h-7 py-1 transition-colors dark:text-gray-200 hover:bg-blue-50 hover:text-blue-600 dark:hover:bg-blue-500/10 dark:hover:text-blue-400 dark:active:bg-blue-500/15 dark:active:text-blue-400"
          >
            <mat-icon
              class="text-gray-600 dark:text-gray-400 transition-colors mr-2 text-sm"
              >calendar_today</mat-icon
            >
            <span class="text-xs font-medium">Calendario</span>
          </a>

          <!-- Enlace directo a Ventas -->
          <a
            mat-list-item
            routerLink="/ventas"
            routerLinkActive="active"
            class="mx-0 my-0 rounded text-gray-700 flex items-center h-auto min-h-7 py-1 transition-colors dark:text-gray-200 hover:bg-blue-50 hover:text-blue-600 dark:hover:bg-blue-500/10 dark:hover:text-blue-400 dark:active:bg-blue-500/15 dark:active:text-blue-400"
          >
            <mat-icon
              class="text-gray-600 dark:text-gray-400 transition-colors mr-2 text-sm"
              >shopping_cart</mat-icon
            >
            <span class="text-xs font-medium">Ventas</span>
          </a>

          <!-- Preguntas y Respuestas-->
          <a
            mat-list-item
            routerLink="faq"
            routerLinkActive="active"
            class="mx-0 my-0 rounded text-gray-700 flex items-center h-auto min-h-7 py-1 transition-colors dark:text-gray-200 hover:bg-blue-50 hover:text-blue-600 dark:hover:bg-blue-500/10 dark:hover:text-blue-400 dark:active:bg-blue-500/15 dark:active:text-blue-400"
          >
            <mat-icon
              class="text-gray-600 dark:text-gray-400 transition-colors mr-2 text-sm"
              >question_answer</mat-icon
            >
            <span class="text-xs font-medium">Foro</span>
          </a>

          <!-- Mis Cursos -->
          <a
            mat-list-item
            routerLink="cursos/mis-cursos"
            routerLinkActive="active"
            class="mx-0 my-0 rounded text-gray-700 flex items-center h-auto min-h-7 py-1 transition-colors dark:text-gray-200 hover:bg-blue-50 hover:text-blue-600 dark:hover:bg-blue-500/10 dark:hover:text-blue-400 dark:active:bg-blue-500/15 dark:active:text-blue-400"
          >
            <mat-icon
              class="text-gray-600 dark:text-gray-400 transition-colors mr-2 text-sm"
              >school</mat-icon
            >
            <span class="text-xs font-medium">Mis Cursos</span>
          </a>

          <!-- Encuestas -->
          <a
            mat-list-item
            routerLink="/encuestas"
            routerLinkActive="active"
            class="mx-0 my-0 rounded text-gray-700 flex items-center h-auto min-h-7 py-1 transition-colors dark:text-gray-200 hover:bg-blue-50 hover:text-blue-600 dark:hover:bg-blue-500/10 dark:hover:text-blue-400 dark:active:bg-blue-500/15 dark:active:text-blue-400"
          >
            <mat-icon
              class="text-gray-600 dark:text-gray-400 transition-colors mr-2 text-sm"
              >quiz</mat-icon
            >
            <span class="text-xs font-medium">Encuestas</span>
          </a>
        </ng-container>
      </ng-template>
    </ng-container>

    <!-- Opción de Login cuando no está autorizado -->
    <a
      *ngIf="!isAuthorized"
      mat-list-item
      routerLink="auth/login"
      routerLinkActive="active"
      class="mx-0 my-0 rounded text-gray-700 flex items-center h-auto min-h-7 py-1 transition-colors dark:text-gray-200 hover:bg-blue-50 hover:text-blue-600 dark:hover:bg-blue-500/10 dark:hover:text-blue-400 dark:active:bg-blue-500/15 dark:active:text-blue-400"
    >
      <mat-icon
        class="text-gray-600 dark:text-gray-400 transition-colors mr-2 text-sm"
        >login</mat-icon
      >
      <span class="text-xs font-medium">Inicio Sesión</span>
    </a>
  </mat-nav-list>

  <!-- Botón para cerrar sesión, solo si está autorizado -->
  <div
    class="flex justify-center p-2 border-t border-blue-900/10 w-full m-0 relative bottom-0 dark:border-blue-500/10"
    *ngIf="isAuthorized"
  >
    <button
      mat-raised-button
      color="warn"
      (click)="onSignOut()"
      class="w-full rounded text-white font-medium tracking-wide text-xs uppercase transition-colors bg-red-600 hover:bg-red-700 dark:bg-red-600/80 dark:hover:bg-red-600 py-1"
    >
      Cerrar sesión
    </button>
  </div>
</div>
