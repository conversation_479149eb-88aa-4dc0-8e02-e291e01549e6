<mat-sidenav-container
  [ngClass]="{ 'h-screen w-full p-0 m-0': isLoginRoute$ | async }"
  class="h-screen w-full bg-slate-50 overflow-hidden dark:bg-[#0a1628] relative"
>
  <!-- Spinner global -->
  <div
    class="fixed inset-0 flex items-center justify-center z-50 bg-black/30"
    *ngIf="showSpinner"
  >
    <app-spinner></app-spinner>
  </div>

  <!-- Menu lateral -->
  <mat-sidenav
    #sidenav
    [mode]="isMobile ? 'over' : 'side'"
    [opened]="
      !isMobile &&
      ((user$ | async)?.role?.trim()?.toUpperCase() === 'ADMIN' ||
        (user$ | async)?.role?.trim()?.toUpperCase() === 'BACKOFFICE') &&
      (showMenu$ | async)
    "
    [class.mobile-sidenav]="isMobile"
    (openedChange)="sidenavStateService.setSidenavState($event)"
    class="w-[220px] min-w-[220px] bg-white border-r border-blue-900/10 shadow-none transition-all duration-300 overflow-hidden dark:bg-[#0a1628] dark:border-blue-500/20"
  >
    <app-menu-list
      (menuToggle)="sidenav.toggle(); sidenavStateService.toggleSidenav()"
      (signOut)="onSignOut(sidenav)"
      [isAuthorized]="(isAuthorized$ | async) || false"
      [user]="user$ | async"
    >
    </app-menu-list>
  </mat-sidenav>

  <!-- Contenido principal -->
  <mat-sidenav-content class="p-0 overflow-hidden flex flex-col">
    <!-- Header / Barra superior (oculto en la página de login) -->
    <app-header
      *ngIf="showMenu$ | async"
      (menuToggle)="sidenav.toggle(); sidenavStateService.toggleSidenav()"
      [isAuthorized]="(isAuthorized$ | async) || false"
      [user]="user$ | async"
      (signOut)="onSignOut(sidenav)"
      [isMobile]="isMobile"
    >
    </app-header>

    <main
      [ngClass]="{
        'p-0 min-h-screen h-screen': isLoginRoute$ | async,
        'p-4 min-h-[calc(100vh-56px)] h-[calc(100vh-56px)]': !(
          isLoginRoute$ | async
        )
      }"
      class="bg-slate-50 transition-colors duration-300 overflow-y-auto w-full dark:bg-[#0a1628]"
    >
      <!-- Rutas principales -->
      <router-outlet></router-outlet>
    </main>
  </mat-sidenav-content>
</mat-sidenav-container>
