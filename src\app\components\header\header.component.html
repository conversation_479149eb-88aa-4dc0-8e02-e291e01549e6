<mat-toolbar
  class="flex items-center justify-between h-14 px-4  border-blue-900/10 shadow-none transition-all duration-300 dark:bg-[#0a1628] dark:border-blue-500/10"
>
  <!-- Contenedor izquierdo con logo y menú -->
  <div class="flex items-center">
    <!-- <PERSON><PERSON><PERSON> de <PERSON> (izquierda) -->
    <button
      mat-icon-button
      (click)="onMenuToggleDispatch()"
      class="focus:outline-none flex items-center justify-center"
    >
      <mat-icon class="text-blue-900 dark:text-blue-100">menu</mat-icon>
    </button>

    <!-- Título del CRM -->
    <div
      class="ml-3 text-base md:text-lg font-medium text-blue-900 dark:text-blue-100 whitespace-nowrap"
    >
      <span>Bienvenido al CRM</span>
    </div>
  </div>

  <!-- Contenedor central para ventas pendientes -->
  <div
    class="flex items-center justify-center"
    *ngIf="
      isBACKOFFICETRAMITADOR() || isBACKOFFICE() || isBACKOFFICESEGUIMIENTO()
    "
  >
    <div class="flex items-center gap-1.5 md:gap-2.5 scale-90 md:scale-100">
      <!-- Dot con animación de pulso -->
      <span class="relative flex h-3 w-3">
        <span
          class="animate-ping absolute inline-flex h-full w-full rounded-full bg-green-500 opacity-75"
        ></span>
        <span
          class="relative inline-flex rounded-full h-3 w-3 bg-green-600"
        ></span>
      </span>
      <span
        class="text-blue-900 dark:text-blue-100 text-xs md:text-sm whitespace-nowrap"
        >Ventas Pendientes:</span
      >
      <span
        class="px-2 py-1 text-red-600 font-bold bg-red-100 rounded-md dark:bg-red-900/20 dark:text-red-400 text-xs md:text-sm"
        >{{ ventasPendientes }}</span
      >
    </div>
  </div>

  <!-- Contenedor derecho con iconos y perfil -->
  <div class="flex items-center gap-2 md:gap-3">
    <!-- Nuevo componente de notificaciones WebSocket -->
    <app-boton-notificaciones *ngIf="isAuthorized"></app-boton-notificaciones>

    <!-- Botón de tema oscuro -->
    <button
      mat-icon-button
      class="w-8 h-8 md:w-9 md:h-9 flex items-center justify-center rounded-full hover:bg-blue-900/5 transition-colors focus:outline-none dark:hover:bg-blue-500/10"
      (click)="toggleDarkMode()"
      [matTooltip]="
        isDarkMode ? 'Cambiar a tema claro' : 'Cambiar a tema azulino'
      "
    >
      <mat-icon
        class="text-blue-900 dark:text-blue-100 text-[20px] md:text-[22px]"
        >{{ isDarkMode ? "light_mode" : "dark_mode" }}</mat-icon
      >
    </button>

    <!-- Horario de Perú y España -->
    <div
      class="hidden lg:flex items-center gap-1 px-2 py-1 text-xs font-medium rounded-md hover:bg-blue-900/5 transition-colors dark:hover:bg-blue-500/10 dark:text-blue-100"
      [class.hidden]="isMobile"
    >
      <mat-icon
        class="text-blue-900 text-base h-[16px] w-[16px] dark:text-blue-100"
        >schedule</mat-icon
      >
      <span class="text-blue-900 dark:text-blue-100"
        >PER {{ horaPeru | date : "h:mm a" }}</span
      >
      <span class="text-gray-500 mx-0.5 dark:text-gray-400">|</span>
      <span class="text-blue-900 dark:text-blue-100"
        >ESP {{ horaEspana | date : "h:mm a" }}</span
      >
    </div>

    <!-- Card de usuario con imagen -->
    <div
      *ngIf="isAuthorized"
      class="flex items-center px-2 md:px-3 py-1.5 ml-1 rounded-lg  border border-blue-900/10 shadow-sm transition-all duration-200 hover:bg-blue-50 dark:bg-[#0a1628] dark:border-blue-500/20 dark:hover:bg-blue-800/20"
    >
      <!-- Icono de perfil de usuario -->
      <div
        class="relative w-7 h-7 md:w-8 md:h-8 mr-1.5 md:mr-2.5 rounded-full flex items-center justify-center bg-blue-900/5 overflow-hidden dark:bg-blue-500/10"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
          stroke-width="1.5"
          class="w-full h-full text-blue-900 opacity-90 dark:text-blue-100"
          stroke="currentColor"
          aria-hidden="true"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            d="M17.982 18.725A7.488 7.488 0 0012 15.75a7.488 7.488 0 00-5.982 2.975m11.963 0a9 9 0 10-11.963 0m11.963 0A8.966 8.966 0 0112 21a8.966 8.966 0 01-5.982-2.275M15 9.75a3 3 0 11-6 0 3 3 0 016 0z"
          ></path>
        </svg>
      </div>

      <!-- Nombre de usuario -->
      <span
        class="hidden md:block text-sm font-semibold text-blue-900 truncate max-w-[100px] lg:max-w-[150px] dark:text-blue-100"
        [class.hidden]="isMobile"
      >
        {{ user?.nombre }} {{ user?.apellido }}
      </span>

      <!-- Estado del usuario -->
      <app-user-status *ngIf="user && user.id"></app-user-status>
    </div>
  </div>
</mat-toolbar>
